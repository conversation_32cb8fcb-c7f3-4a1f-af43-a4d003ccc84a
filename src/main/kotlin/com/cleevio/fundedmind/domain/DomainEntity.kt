package com.cleevio.fundedmind.domain

import com.cleevio.fundedmind.domain.common.exception.EntityIsDeletedException
import jakarta.persistence.Column
import jakarta.persistence.EntityListeners
import jakarta.persistence.Id
import jakarta.persistence.MappedSuperclass
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import org.springframework.data.util.ProxyUtils
import java.time.Clock
import java.time.Instant
import java.util.UUID

@MappedSuperclass
abstract class DomainEntity(
    @Id val id: UUID,
) {
    override fun equals(other: Any?): Boolean {
        other ?: return false
        if (this === other) return true
        if (javaClass != ProxyUtils.getUserClass(other)) return false
        other as DomainEntity
        return this.id == other.id
    }

    override fun hashCode(): Int = id.hashCode()
}

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class CreatableEntity(id: UUID) : DomainEntity(id) {
    @CreatedDate
    @Column(name = "created_at", updatable = false, nullable = false)
    private var createdTimestamp: Instant = Instant.now()

    val createdAt: Instant
        get() = createdTimestamp

    @CreatedBy
    @Column(name = "created_by", updatable = false, nullable = true)
    private var createdByUser: UUID? = null

    val createdBy: UUID?
        get() = createdByUser
}

@MappedSuperclass
abstract class UpdatableEntity(id: UUID) : CreatableEntity(id) {
    @LastModifiedDate
    @Column(name = "updated_at", updatable = true, nullable = false)
    private var updatedTimestamp: Instant = Instant.now()

    val updatedAt: Instant
        get() = updatedTimestamp

    @LastModifiedBy
    @Column(name = "updated_by", updatable = true, nullable = true)
    private var updatedByUser: UUID? = null

    val updatedBy: UUID?
        get() = updatedByUser
}

@MappedSuperclass
abstract class SoftDeletableEntity(id: UUID) : UpdatableEntity(id) {
    private var deletedAt: Instant? = null

    val isDeleted: Boolean
        get() = deletedAt != null

    fun softDelete(clock: Clock = Clock.systemDefaultZone()) {
        if (!isDeleted) {
            deletedAt = Instant.now(clock)
        }
    }

    fun restore() {
        deletedAt = null
    }

    fun checkNonDeleted() {
        if (isDeleted) throw EntityIsDeletedException("Entity ${this.javaClass::class.simpleName} is deleted.")
    }
}
