package com.cleevio.fundedmind.domain.gamelevelreward.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class GameLevelRewardNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_LEVEL_REWARD_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class GameLevelRewardMissingPictureException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_LEVEL_REWARD_PICTURE_MISSING,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class GameLevelRewardButtonWithoutLinkException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_LEVEL_REWARD_BUTTON_WITHOUT_LINK,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ActiveGameLevelRewardsMismatchException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.ACTIVE_GAME_LEVEL_REWARDS_MISMATCH,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class GameLevelRewardOrderCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_LEVEL_REWARD_ORDER_CANNOT_BE_NEGATIVE,
    message = message,
)
