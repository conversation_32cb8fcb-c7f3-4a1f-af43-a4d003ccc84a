package com.cleevio.fundedmind.domain.gamelevelreward

import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CreateGameLevelRewardService(
    private val gameLevelRewardRepository: GameLevelRewardRepository,
) {

    @Transactional
    fun create(
        name: String,
        gameLevel: GameLevel,
        type: GameLevelRewardType,
        description: String?,
        rewardCouponCode: String?,
        rewardButton: AppButtonWithLink?,
        listingOrder: Int = 0,
    ): GameLevelReward = gameLevelRewardRepository.save(
        GameLevelReward.newGameLevelReward(
            name = name,
            gameLevel = gameLevel,
            type = type,
            description = description,
            rewardCouponCode = rewardCouponCode,
            rewardButton = rewardButton,
            listingOrder = listingOrder,
        ),
    )
}
