package com.cleevio.fundedmind.domain.user.appuser

import com.cleevio.fundedmind.application.common.type.FirebaseId
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CreateAppUserService(
    private val userRepository: AppUserRepository,
) {
    @Transactional
    fun createStudentAccount(
        firebaseIdentifier: FirebaseId,
        email: String,
        hubspotIdentifier: HubspotId,
        stripeIdentifier: StripeCustomerId,
        traderReferral: String?,
    ): AppUser = userRepository.save(
        AppUser.newStudentAccount(
            email = email,
            firebaseIdentifier = firebaseIdentifier,
            hubspotIdentifier = hubspotIdentifier,
            stripeIdentifier = stripeIdentifier,
            traderReferral = traderReferral,
        ),
    )

    @Transactional
    fun createAdminAccount(
        firebaseIdentifier: FirebaseId,
        email: String,
        hubspotIdentifier: Hu<PERSON>potId,
    ): AppUser = userRepository.save(
        AppUser.newAdminAccount(
            email = email,
            firebaseIdentifier = firebaseIdentifier,
            hubspotIdentifier = hubspotIdentifier,
        ),
    )
}
