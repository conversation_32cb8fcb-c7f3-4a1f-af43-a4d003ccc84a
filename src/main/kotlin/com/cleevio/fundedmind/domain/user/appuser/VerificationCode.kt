package com.cleevio.fundedmind.domain.user.appuser

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.application.module.user.appuser.exception.VerificationCodeHasWrongStatusException
import com.cleevio.fundedmind.domain.DomainEntity
import com.cleevio.fundedmind.domain.user.appuser.constant.VerificationCodeStatus
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Table(name = "app_user_verification_code")
@Entity
@DynamicUpdate
class VerificationCode private constructor(
    id: UUID,
    expiresAt: Instant,
    val appUserId: UUID,
    val code: String,
    private var usedAt: Instant?,
) : DomainEntity(id) {

    var expiresAt: Instant = expiresAt
        private set

    private val isUsed: <PERSON>olean
        get() = usedAt != null

    private val isExpired: Boolean
        get() = Instant.now().isAfter(expiresAt)

    val status: VerificationCodeStatus
        get() = when {
            isUsed -> VerificationCodeStatus.USED
            isExpired -> VerificationCodeStatus.EXPIRED
            else -> VerificationCodeStatus.VALID
        }

    companion object {
        fun newVerificationCode(
            id: UUID = UUIDv7.randomUUID(),
            appUserId: UUID,
            code: String,
            expiresAt: Instant,
        ) = VerificationCode(
            id = id,
            appUserId = appUserId,
            code = code,
            expiresAt = expiresAt,
            usedAt = null,
        ).also {
            require(code.length == 4)
        }
    }

    fun expire() {
        checkStatus(VerificationCodeStatus.VALID)
        this.expiresAt = Instant.now()
    }

    fun useCode() {
        checkStatus(VerificationCodeStatus.VALID)
        markAsUsed()
    }

    private fun checkStatus(expectedStatus: VerificationCodeStatus) {
        if (this.status != expectedStatus) {
            throw VerificationCodeHasWrongStatusException(
                "Cannot use verification code: '$code' '$id' because it is not VALID.",
            )
        }
    }

    internal fun markAsUsed() { // internal for testing purposes
        this.usedAt = Instant.now()
    }
}

@Repository
interface VerificationCodeRepository : JpaRepository<VerificationCode, UUID> {
    fun findByCodeAndAppUserId(
        code: String,
        appUserId: UUID,
    ): VerificationCode?

    fun existsByCodeAndAppUserId(
        code: String,
        appUserId: UUID,
    ): Boolean

    fun findAllByAppUserId(appUserId: UUID): List<VerificationCode>
}
