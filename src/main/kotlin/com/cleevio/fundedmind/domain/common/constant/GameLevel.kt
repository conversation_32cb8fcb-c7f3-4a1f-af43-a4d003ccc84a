package com.cleevio.fundedmind.domain.common.constant

import com.cleevio.fundedmind.application.common.util.isNegative
import java.math.BigDecimal

enum class GameLevel(val order: Int, val levelPayoutGoal: Int?) {
    ZERO(order = 0, levelPayoutGoal = null),
    ONE(order = 1, levelPayoutGoal = null),
    TWO(order = 2, levelPayoutGoal = null),
    THREE(order = 3, levelPayoutGoal = null),
    FOUR(order = 4, levelPayoutGoal = 0), // first payout can be of any value therefore zero amount
    FIVE(order = 5, levelPayoutGoal = 5_000),
    SIX(order = 6, levelPayoutGoal = 10_000),
    SEVEN(order = 7, levelPayoutGoal = 20_000),
    EIGHT(order = 8, levelPayoutGoal = 50_000),
    NINE(order = 9, levelPayoutGoal = 100_000),
    TEN(order = 10, levelPayoutGoal = null),
    ;

    /**
     * Returns the cumulative amount needed to complete all levels up to the current level.
     * Examples:
     *  - level 1/2/3/4: 0
     *  - level 5:      5k
     *  - level 6:     15k = 5k+10k
     *  - level 7:     35k = 5k+10k+20k
     *  - level 8:     85k = 5k+10k+20k+50k
     *  - level 9/10: 185k = 5k+10k+20k+50k+100k
     */
    val cumulativeAmountUntilCurrentLevel: BigDecimal
        get() = entries
            .filter { it.order <= this.order }
            .filter { it.levelPayoutGoal != null }
            .sortedBy { it.order }
            .sumOf { it.levelPayoutGoal!!.toBigDecimal() }

    companion object {

        /**
         * Returns a list of levels that a student needs to gain to reach the reachedLevel.
         *
         * E.g.
         * - if currentLevel = 4; reachedLevel = 6; it returns [5, 6]
         * - if currentLevel = 4; reachedLevel = 4; it returns empty list
         * - if currentLevel = 6; reachedLevel = 4; it returns empty list
         *
         * @param currentLevel The current level of the student
         * @param reachedLevel The level that the student has reached
         * @return The list of levels that the student needs to gain, sorted by order
         */
        fun levelsToGain(
            currentLevel: GameLevel,
            reachedLevel: GameLevel,
        ): List<GameLevel> = entries
            .filter { it.order in currentLevel.order + 1..reachedLevel.order }
            .sortedBy { it.order }

        /**
         * Determines what level would be reached with a given payout amount.
         *
         * @param payoutAmount The total payout amount
         * @return The level that would be reached with the given amount
         */
        fun determineLevelByPayoutAmount(payoutAmount: BigDecimal): GameLevel {
            check(!payoutAmount.isNegative()) { "Payout amount cannot be negative: $payoutAmount" }

            val levelsWithGoals = GameLevel
                .entries
                .filter { it.levelPayoutGoal != null }
                .sortedBy { it.order }

            var cumulativeAmount = BigDecimal.ZERO
            var lastCompletedLevel: GameLevel? = null

            for (level in levelsWithGoals) {
                val goalAmount = level.levelPayoutGoal!!.toBigDecimal()

                if (cumulativeAmount + goalAmount <= payoutAmount) {
                    // This level is completed
                    lastCompletedLevel = level
                    cumulativeAmount += goalAmount
                } else {
                    // This level is not completed, return the last completed level
                    break
                }
            }

            // Return the last completed level, or the first level if none are completed
            return requireNotNull(lastCompletedLevel) {
                "Last completed level is null. This should not happen."
            }
        }
    }
}
