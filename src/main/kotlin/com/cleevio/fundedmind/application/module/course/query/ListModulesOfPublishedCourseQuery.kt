package com.cleevio.fundedmind.application.module.course.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.CourseModuleState
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class ListModulesOfPublishedCourseQuery(
    val userId: UUID,
    val courseId: UUID,
) : Query<ListModulesOfPublishedCourseQuery.Result> {

    @Schema(name = "ListModulesOfPublishedCourseResult")
    data class Result(
        val data: List<PublishedCourseModule>,
    )

    @Schema(name = "ListModulesOfPublishedCourseModule")
    data class PublishedCourseModule(
        val moduleId: UUID,
        val listingOrder: Int,
        val totalDurationInSeconds: Long,
        val title: String,
        val description: String,
        val thumbnailDesktop: ImageResult?,
        val thumbnailMobile: ImageResult?,
        val lessonCount: Int,
        val isLockedForMe: Boolean,
        val unlockedData: PublishedCourseModuleUnlockedData?,
    )

    @Schema(name = "ListModulesOfPublishedCourseUnlockedData")
    data class PublishedCourseModuleUnlockedData(
        val courseModuleState: CourseModuleState,
        val watchLessonId: UUID?,
        val finishedLessons: Int,
    )
}
