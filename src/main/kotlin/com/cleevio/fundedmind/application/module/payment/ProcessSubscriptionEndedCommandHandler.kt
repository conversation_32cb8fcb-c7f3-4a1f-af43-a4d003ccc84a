package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionEndedCommand
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.service.DeactivateStudentDiscordSubscriptionService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service

@Service
class ProcessSubscriptionEndedCommandHandler(
    private val appUserFinderService: AppUserFinderService,
    private val deactivateStudentDiscordSubscriptionService: DeactivateStudentDiscordSubscriptionService,
    private val stripeProperties: StripeProperties,
) : CommandHandler<Unit, ProcessSubscriptionEndedCommand> {
    override val command = ProcessSubscriptionEndedCommand::class

    private val logger = logger()

    @SentrySpan
    override fun handle(command: ProcessSubscriptionEndedCommand) {
        logger.info("Processing end of subscription: '${command.subscriptionIdentifier}'...")

        when (command.productIdentifier) {
            stripeProperties.product.discord -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for DISCORD product.")
                processDiscordSubscriptionEnded(command)
            }

            stripeProperties.product.masterclass -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for MASTERCLASS product.")

                // by ending the subscription we expect that the masterclass has been either paid in full or canceled
                processMasterclassSubscriptionEnded(command)
            }

            stripeProperties.product.exclusive -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' for EXCLUSIVE product. " +
                        "This case should not be viable as EXCLUSIVE shouldn't be paid via subscription model.",
                )
            }

            else -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' is " +
                        "related to unknown product: '${command.productIdentifier}'.",
                )
                return
            }
        }

        logger.info("End of Subscription: '${command.subscriptionIdentifier}' successfully processed.")
    }

    private fun processMasterclassSubscriptionEnded(command: ProcessSubscriptionEndedCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)

        logger.info(
            "Subscription: '${command.subscriptionIdentifier}' for MASTERCLASS product " +
                "ended for student: '${studentUser.id}'.",
        )
    }

    private fun processDiscordSubscriptionEnded(command: ProcessSubscriptionEndedCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)

        deactivateStudentDiscordSubscriptionService.deactivate(userId = studentUser.id)
    }
}
