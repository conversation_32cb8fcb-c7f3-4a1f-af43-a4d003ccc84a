package com.cleevio.fundedmind.application.module.highlight.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.highlight.Highlight
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.highlight.exception.HighlightNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class HighlightFinderService(
    private val highlightRepository: HighlightRepository,
) : BaseFinderService<Highlight>(highlightRepository) {

    override fun errorBlock(message: String) = throw HighlightNotFoundException(message)

    override fun getEntityType() = Highlight::class

    fun findAllNonDeleted(): List<Highlight> = highlightRepository.findAllByDeletedAtIsNull()

    fun findMaxListingOrderNonDeleted(): Int? = highlightRepository.findMaxListingOrderNonDeleted()

    fun countNonDeleted(): Long = highlightRepository.countAllByDeletedAtIsNull()
}
