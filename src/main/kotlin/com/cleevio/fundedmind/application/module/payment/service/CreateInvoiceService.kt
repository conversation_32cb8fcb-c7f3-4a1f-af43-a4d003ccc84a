package com.cleevio.fundedmind.application.module.payment.service

import com.cleevio.fundedmind.application.common.port.out.GetUserPort
import com.cleevio.fundedmind.application.common.port.out.PaymentCustomerPort
import com.cleevio.fundedmind.application.common.port.out.PaymentInvoicePort
import com.cleevio.fundedmind.application.common.type.FakturoidInvoiceId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.application.module.payment.port.out.InvoicingPort
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Service
class CreateInvoiceService(
    private val appUserFinderService: AppUserFinderService,
    private val studentFinderService: StudentFinderService,
    private val paymentInvoicePort: PaymentInvoicePort,
    private val invoicingPort: InvoicingPort,
    private val getUserPort: GetUserPort,
    private val paymentCustomerPort: PaymentCustomerPort,
) {
    fun createInvoice(
        invoiceIdentifier: StripeInvoiceId,
        customerIdentifier: StripeCustomerId,
        issuedOn: LocalDate,
    ): FakturoidInvoiceId {
        val user = appUserFinderService
            .findByStripeIdentifier(customerIdentifier)
            ?.apply { this.checkRole(UserRole.STUDENT) }

        // get email and name from our DB, if not found then use Stripe values as fallbacke
        val (email: String, fullName: String) = if (user != null) {
            if (getUserPort(user.id).onboardingFinished) {
                val student = studentFinderService.getById(user.id)
                user.email to student.fullName
            } else {
                user.email to user.email
            }
        } else {
            val customerInfo = paymentCustomerPort.getCustomerBasicInfo(customerIdentifier)
            requireNotNull(customerInfo.email) to requireNotNull(customerInfo.name)
        }

        val stripeInvoice = paymentInvoicePort.getInvoiceDataById(invoiceIdentifier)

        val invoiceRequest = InvoicingPort.InvoiceCreationRequest(
            name = fullName,
            customId = invoiceIdentifier,
            email = email,
            street = stripeInvoice.street,
            city = stripeInvoice.city,
            zip = stripeInvoice.postalCode,
            country = stripeInvoice.countryCode,
            ico = stripeInvoice.ico,
            dic = stripeInvoice.dic,
            issuedOn = issuedOn.format(DateTimeFormatter.ISO_DATE),
            products = stripeInvoice.products.map {
                InvoicingPort.InvoiceCreationRequest.Product(
                    name = it.description,
                    price = it.currency.toAmount(it.unitAmount).toDouble(),
                    taxBehaviour = it.taxBehaviour,
                    vatRate = it.vatRate,
                )
            },
        )

        // create Fakturoid invoice
        val createdInvoiceId = invoicingPort.createInvoice(invoiceRequest)

        return createdInvoiceId
    }
}
