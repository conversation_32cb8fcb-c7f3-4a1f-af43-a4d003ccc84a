package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.student.command.StudentUpdatesQuestionnaireCommand
import com.cleevio.fundedmind.application.module.user.student.event.StudentQuestionnaireUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.user.toQuestionnaire
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StudentUpdatesQuestionnaireCommandHandler(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, StudentUpdatesQuestionnaireCommand> {
    override val command = StudentUpdatesQuestionnaireCommand::class

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentUpdatesQuestionnaireCommand) {
        studentFinderService
            .getById(command.studentId)
            .apply { updateQuestionnaire(command.questionnaire.toQuestionnaire()) }
            .also { student ->
                applicationEventPublisher.publishEvent(
                    StudentQuestionnaireUpdatedEvent(studentId = student.id),
                )
            }
    }
}
