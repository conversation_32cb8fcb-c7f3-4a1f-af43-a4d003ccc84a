package com.cleevio.fundedmind.application.module.gamelevelprogress.query

import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class StudentGetsGameLevelProgressQuery(
    val studentId: UUID,
) : Query<StudentGetsGameLevelProgressQuery.Result> {

    @Schema(name = "StudentGetsGameLevelProgressResult")
    data class Result(
        val data: List<GameLevelProgressItem>,
    )

    @Schema(name = "GameLevelProgressItem")
    data class GameLevelProgressItem(
        val id: UUID,
        val level: GameLevel,
        val achievedAt: Instant,
        val shown: Boolean,
    )
}
