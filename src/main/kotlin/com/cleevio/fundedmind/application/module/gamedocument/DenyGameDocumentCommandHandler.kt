package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.gamedocument.command.DenyGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentDeniedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DenyGameDocumentCommandHandler(
    private val gameDocumentFinderService: GameDocumentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, DenyGameDocumentCommand> {

    override val command = DenyGameDocumentCommand::class

    @Transactional
    @Lock(module = Locks.GameDocument.MODULE, lockName = Locks.GameDocument.UPDATE)
    override fun handle(@LockFieldParameter("gameDocumentId") command: DenyGameDocumentCommand) {
        gameDocumentFinderService
            .getById(command.gameDocumentId)
            .apply { checkStateIn(GameDocumentApprovalState.WAITING) }
            .apply { deny(message = command.denyMessage, now = command.now) }
            .also { applicationEventPublisher.publishEvent(GameDocumentDeniedEvent(gameDocumentId = it.id)) }
    }
}
