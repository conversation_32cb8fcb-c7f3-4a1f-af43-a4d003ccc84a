package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.port.out.GetAppUserForInternalPort
import com.cleevio.fundedmind.application.common.port.out.GetNetworkingPeoplePort
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.networking.query.SearchPeopleInNetworkingQuery
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SearchPeopleInNetworkingQueryHandler(
    private val getAppUserForInternalPort: GetAppUserForInternalPort,
    private val getNetworkingPeoplePort: GetNetworkingPeoplePort,
) : QueryHandler<InfiniteScrollSlice<SearchPeopleInNetworkingQuery.Result, UUID>, SearchPeopleInNetworkingQuery> {

    override val query = SearchPeopleInNetworkingQuery::class

    @SentrySpan
    override fun handle(
        query: SearchPeopleInNetworkingQuery,
    ): InfiniteScrollSlice<SearchPeopleInNetworkingQuery.Result, UUID> {
        val meUser = getAppUserForInternalPort(query.userId)

        return getNetworkingPeoplePort.searchPeople(
            meUserId = query.userId,
            filter = query.filter,
            infiniteScroll = query.infiniteScroll,
            includeLastNameMapper = { meUser.canSeeLastName },
            includeObfuscatedLocation = { false },
        )
    }
}
