package com.cleevio.fundedmind.application.module.payment.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUpdatePaymentTierStatePort
import com.cleevio.fundedmind.application.module.payment.event.ExclusivePurchasedEvent
import com.cleevio.fundedmind.application.module.payment.event.MasterclassPurchasedEvent
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
class PaymentCrmEventListener(
    private val crmUpdatePaymentTierStatePort: CrmUpdatePaymentTierStatePort,
    private val appUserFinderService: AppUserFinderService,
) {

    @SentryTransaction(operation = "async.crm.masterclass-purchased")
    @Async
    @EventListener
    fun handleMasterclassPurchasedEvent(event: MasterclassPurchasedEvent) {
        val appUser = appUserFinderService.getById(event.userId)

        crmUpdatePaymentTierStatePort.updateCrmPaymentTierState(
            hubspotIdentifier = appUser.hubspotIdentifier,
            paymentTierState = event.paymentTierState,
        )
    }

    @SentryTransaction(operation = "async.crm.exclusive-purchased")
    @Async
    @EventListener
    fun handleExclusivePurchasedEvent(event: ExclusivePurchasedEvent) {
        val appUser = appUserFinderService.getById(event.userId)

        crmUpdatePaymentTierStatePort.updateCrmPaymentTierState(
            hubspotIdentifier = appUser.hubspotIdentifier,
            paymentTierState = event.paymentTierState,
        )
    }
}
