package com.cleevio.fundedmind.application.module.file

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.file.command.UserDeletesTheirProfilePictureCommand
import com.cleevio.fundedmind.application.module.file.exception.ImageDeleteFailedException
import com.cleevio.fundedmind.application.module.file.replacer.AppFileReplacerFactory
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class UserDeletesTheirProfilePictureCommandHandler(
    private val appFileReplacerFactory: AppFileReplacerFactory,
) : CommandHandler<Unit, UserDeletesTheirProfilePictureCommand> {
    override val command = UserDeletesTheirProfilePictureCommand::class

    @SentrySpan
    @Transactional
    @Lock(module = Locks.File.MODULE, lockName = Locks.File.UPDATE)
    override fun handle(@LockFieldParameter("userId") command: UserDeletesTheirProfilePictureCommand) {
        if (!command.type.isProfilePicture()) {
            throw ImageDeleteFailedException("Image type: '${command.type}' is not a profile picture type.")
        }

        appFileReplacerFactory
            .getFor(fileType = command.type)
            .deleteFile(entityReference = command.userId)
    }
}
