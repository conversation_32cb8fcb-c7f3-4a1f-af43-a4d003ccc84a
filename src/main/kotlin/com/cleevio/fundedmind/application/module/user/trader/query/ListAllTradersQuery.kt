package com.cleevio.fundedmind.application.module.user.trader.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class ListAllTradersQuery(
    @field:Valid val filter: Filter,
) : Query<ListAllTradersQuery.Result> {

    data class Filter(
        override val searchString: String?,
    ) : AutocompleteFilter

    @Schema(name = "ListAllTradersResult")
    data class Result(
        val data: List<TraderListing>,
    )

    @Schema(name = "ListAllTradersTrader")
    data class TraderListing(
        val traderId: UUID,
        val active: <PERSON><PERSON>an,
        val mentoring: TraderMentoring,
        val listingOrder: Int,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
        val firstName: String,
        val lastName: String,
        val calendlyUrl: String?,
        val tags: List<TraderTag>,
    ) {
        val fullName: String
            get() = "$firstName $lastName"
    }
}
