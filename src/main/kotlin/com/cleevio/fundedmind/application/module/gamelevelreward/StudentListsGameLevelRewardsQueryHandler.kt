package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.gamelevelreward.finder.GameLevelRewardFinderService
import com.cleevio.fundedmind.application.module.gamelevelreward.query.StudentListsGameLevelRewardsQuery
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.toResultButton
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StudentListsGameLevelRewardsQueryHandler(
    private val gameLevelRewardFinderService: GameLevelRewardFinderService,
    private val appFileFinderService: AppFileFinderService,
    private val studentFinderService: StudentFinderService,
) : QueryHandler<StudentListsGameLevelRewardsQuery.Result, StudentListsGameLevelRewardsQuery> {

    override val query = StudentListsGameLevelRewardsQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: StudentListsGameLevelRewardsQuery): StudentListsGameLevelRewardsQuery.Result {
        val student = studentFinderService.getById(query.studentId)

        val gameLevelRewards = gameLevelRewardFinderService
            .findAllNonDeleted()
            .filter { it.published }
            .sortedBy { it.listingOrder }

        val rewardItems = gameLevelRewards
            .map { gameLevelReward ->
                val shouldBeBlurred = gameLevelReward.shouldBeBlurred(student.gameLevel)

                StudentListsGameLevelRewardsQuery.GameLevelRewardItem(
                    gameLevelRewardId = gameLevelReward.id,
                    listingOrder = gameLevelReward.listingOrder,
                    name = gameLevelReward.name,
                    gameLevel = gameLevelReward.gameLevel,
                    type = gameLevelReward.type,
                    description = gameLevelReward.description,
                    rewardPhoto = gameLevelReward.rewardPhotoFileId?.let { appFileFinderService.getImageById(it) },
                    rewardCouponCode = shouldBeBlurred.ifFalse { gameLevelReward.rewardCouponCode },
                    rewardButton = shouldBeBlurred.ifFalse { gameLevelReward.rewardButton?.toResultButton() },
                )
            }

        return StudentListsGameLevelRewardsQuery.Result(
            data = rewardItems,
        )
    }
}
