package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.payment.command.ProcessInvoicePaidCommand
import com.cleevio.fundedmind.application.module.payment.port.out.InvoicingPort
import com.cleevio.fundedmind.application.module.payment.service.CreateInvoiceService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProcessInvoicePaidCommandHandler(
    private val invoicingPort: InvoicingPort,
    private val createInvoiceService: CreateInvoiceService,
) : CommandHandler<Unit, ProcessInvoicePaidCommand> {
    override val command = ProcessInvoicePaidCommand::class

    private val logger = logger()

    /**
     * Process invoice paid event
     *
     * Lock is the same as in [ProcessInvoiceFinalizedCommandHandler].
     * Locking is vital because Stripe events might not be delivered in order.
     *
     * Meaning 'invoice.paid' might come before 'invoice.finalized'.
     * In such case, in this handler we need to both create and pay the Fakturoid invoice.
     */
    @SentrySpan
    @Transactional // ! locking is crucial - see above
    @Lock(module = Locks.Payment.MODULE, lockName = Locks.Payment.INVOICE_GENERATION)
    override fun handle(@LockFieldParameter("invoiceIdentifier") command: ProcessInvoicePaidCommand) {
        logger.info("Processing invoice paid for invoice: '${command.invoiceIdentifier}'...")

        val fakturoidInvoice = invoicingPort.findInvoiceIdByCustomId(customId = command.invoiceIdentifier)

        if (fakturoidInvoice == null) {
            logger.info(
                "Invoice: '${command.invoiceIdentifier}' is paid in Stripe but doesn't exist in Fakturoid yet, " +
                    "will try to create it.",
            )

            val fakturoidInvoiceId = createInvoiceService.createInvoice(
                invoiceIdentifier = command.invoiceIdentifier,
                customerIdentifier = command.customerIdentifier,
                issuedOn = command.finalizedAt,
            )

            // Mark the invoice as paid in Fakturoid
            invoicingPort.payInvoice(
                invoiceId = fakturoidInvoiceId,
                paidAt = command.paidAt,
            )

            return
        }

        if (fakturoidInvoice.status == "paid") {
            logger.info("Invoice: '${command.invoiceIdentifier}' is already paid in Fakturoid, skipping processing.")
            return
        }

        // Mark the invoice as paid in Fakturoid
        invoicingPort.payInvoice(
            invoiceId = fakturoidInvoice.id,
            paidAt = command.paidAt,
        )

        logger.info("Successfully processed invoice paid for invoice: '${command.invoiceIdentifier}'")
    }
}
