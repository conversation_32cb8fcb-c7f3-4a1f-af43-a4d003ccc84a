package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentApprovedEvent
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentDeniedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class GameDocumentEmailEventListener(
    private val sendEmailService: SendEmailService,
    private val gameDocumentFinderService: GameDocumentFinderService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameDocumentDeniedEvent(event: GameDocumentDeniedEvent) {
        sendEmailService.sendEmailGameDocumentDenied(event.gameDocumentId)
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameDocumentApprovedEvent(event: GameDocumentApprovedEvent) {
        val gameDocument = gameDocumentFinderService.getById(event.gameDocumentId)

        when (gameDocument.type) {
            GameDocumentType.CERTIFICATE -> sendEmailService.sendEmailGameDocumentCertificateApproved(
                event.gameDocumentId,
            )

            GameDocumentType.BACKTESTING -> sendEmailService.sendEmailGameDocumentBacktestingApproved(
                event.gameDocumentId,
            )

            GameDocumentType.PAYOUT -> {
                if (gameDocument.reachedLevel == GameLevel.NINE) {
                    // level 9 is the last with the progress, so the next level progress email is not suitable
                    sendEmailService.sendEmailGameDocumentPayoutApproved(event.gameDocumentId)
                } else {
                    sendEmailService.sendEmailGameDocumentPayoutProgressApproved(event.gameDocumentId)
                }
            }
        }
    }
}
