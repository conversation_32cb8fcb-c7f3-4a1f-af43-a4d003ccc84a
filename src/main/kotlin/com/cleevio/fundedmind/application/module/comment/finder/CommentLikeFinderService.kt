package com.cleevio.fundedmind.application.module.comment.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.comment.CommentLike
import com.cleevio.fundedmind.domain.comment.CommentLikeRepository
import com.cleevio.fundedmind.domain.comment.exception.CommentLikeNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class CommentLikeFinderService(
    private val commentLikeRepository: CommentLikeRepository,
) : BaseFinderService<CommentLike>(commentLikeRepository) {

    override fun errorBlock(message: String) = throw CommentLikeNotFoundException(message)

    override fun getEntityType() = CommentLike::class

    fun findByCommentIdAndAppUserId(
        commentId: UUID,
        appUserId: UUID,
    ): CommentLike? = commentLikeRepository.findByCommentIdAndAppUserId(commentId, appUserId)

    fun findAllByCommentId(commentId: UUID): List<CommentLike> = commentLikeRepository.findAllByCommentId(commentId)
}
