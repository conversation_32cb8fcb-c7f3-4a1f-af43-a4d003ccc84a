package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.gamedocument.command.UpdateGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentUpdatedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateGameDocumentCommandHandler(
    private val gameDocumentFinderService: GameDocumentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : Command<PERSON>andler<Unit, UpdateGameDocumentCommand> {

    override val command = UpdateGameDocumentCommand::class

    @Transactional
    @Lock(module = Locks.GameDocument.MODULE, lockName = Locks.GameDocument.UPDATE)
    override fun handle(@LockFieldParameter("gameDocumentId") command: UpdateGameDocumentCommand) {
        gameDocumentFinderService
            .getById(command.gameDocumentId)
            .apply {
                update(
                    type = command.type,
                    issuingCompany = command.issuingCompany,
                    payoutAmount = command.payoutAmount,
                    reachedLevel = command.reachedLevel,
                    payoutDate = command.payoutDate,
                    truthScore = command.truthScore,
                    scoreMessage = command.scoreMessage,
                )
            }
            .also { applicationEventPublisher.publishEvent(GameDocumentUpdatedEvent(gameDocumentId = it.id)) }
    }
}
