package com.cleevio.fundedmind.application.module.crm

import com.cleevio.fundedmind.adapter.out.hubspot.connector.request.HubspotFieldType
import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.crm.command.CreateHubspotPropertyCommand
import com.cleevio.fundedmind.application.module.crm.port.out.CrmCreateCustomerPropertyPort
import org.springframework.stereotype.Service

@Service
class CreateHubspotPropertyCommandHandler(
    private val crmCreateCustomerPropertyPort: CrmCreateCustomerPropertyPort,
) : CommandHandler<Unit, CreateHubspotPropertyCommand> {

    override val command = CreateHubspotPropertyCommand::class

    override fun handle(command: CreateHubspotPropertyCommand) {
        when (command.type) {
            HubspotFieldType.NUMBER -> error("Not implemented")

            HubspotFieldType.STRING -> crmCreateCustomerPropertyPort.createCustomerTextProperty(
                command.propertyName,
                command.propertyDisplayedLabel,
            )

            HubspotFieldType.CHECKBOX -> crmCreateCustomerPropertyPort.createCustomerCheckboxProperty(
                command.propertyName,
                command.propertyDisplayedLabel,
            )
        }
    }
}
