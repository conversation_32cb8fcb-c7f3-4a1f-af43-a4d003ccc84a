package com.cleevio.fundedmind.application.module.user.student

import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionProductPricesPort
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.module.user.student.query.GetDiscordSubscriptionPricesQuery
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import org.springframework.stereotype.Component

@Component
class GetDiscordSubscriptionPricesQueryHandler(
    private val getSubscriptionProductPricesPort: GetSubscriptionProductPricesPort,
    private val stripeProperties: StripeProperties,
) : Query<PERSON><PERSON>ler<GetDiscordSubscriptionPricesQuery.Result, GetDiscordSubscriptionPricesQuery> {

    override val query = GetDiscordSubscriptionPricesQuery::class

    override fun handle(query: GetDiscordSubscriptionPricesQuery): GetDiscordSubscriptionPricesQuery.Result {
        val discordProductId: StripeProductId = stripeProperties.product.discord

        return getSubscriptionProductPricesPort
            .getPricesByIdentifier(productIdentifier = discordProductId)
            .map {
                GetDiscordSubscriptionPricesQuery.DiscordSubscriptionPrice(
                    priceId = it.priceIdentifier,
                    price = it.price,
                    recurringInterval = it.recurringInterval,
                    recurringIntervalCount = it.recurringIntervalCount,
                )
            }
            .let { GetDiscordSubscriptionPricesQuery.Result(prices = it) }
    }
}
