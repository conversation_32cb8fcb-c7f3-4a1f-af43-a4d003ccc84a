package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.application.common.port.out.AutocompleteStudentUserPort
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.user.appuser.query.AutocompleteStudentUserQuery
import org.springframework.stereotype.Component

@Component
class AutocompleteStudentUserQueryHandler(
    private val autocompleteStudentUserPort: AutocompleteStudentUserPort,
) : QueryHandler<AutocompleteStudentUserQuery.Result, AutocompleteStudentUserQuery> {

    override val query = AutocompleteStudentUserQuery::class
    override fun handle(query: AutocompleteStudentUserQuery): AutocompleteStudentUserQuery.Result =
        autocompleteStudentUserPort(
            filter = query.filter,
            limit = query.limit,
        )
}
