package com.cleevio.fundedmind.application.module.email

import com.cleevio.fundedmind.application.common.util.formatToCzechDateTime
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import java.math.BigDecimal
import java.time.Instant

enum class EmailType {
    OTP_EMAIL,
    DISCORD_PAYMENT_FAILED,
    DISCORD_ENDED,
    DISCORD_FINISH_CHECKOUT_CONNECT,
    EXCLUSIVE_UPGRADED,
    STUDENT_FINISH_MENTORING_CHECKOUT,
    FINISH_ONBOARDING_BASECAMP,
    FINISH_ONBOARDING_MASTERCLASS,
    COMMENT_THREAD_REACTION,
    NETWORKING_DIRECT_MESSAGE,
    GAME_DOCUMENT_DENIED,
    GAME_DOCUMENT_CERTIFICATE_APPROVED,
    GAME_DOCUMENT_BACKTESTING_APPROVED,
    GAME_DOCUMENT_PAYOUT_APPROVED,
    GAME_DOCUMENT_PAYOUT_PROGRESS_APPROVED,

    // these 2 emails, client did not proof read but they can remain for now in operation
    TRADER_FINISH_MENTORING_CHECKOUT,
    DISCORD_CANCELLED,

    ;

    val emailTemplateName: String = this.name.lowercase()
}

sealed class AppEmail {
    abstract val type: EmailType
    abstract val subject: String
    abstract val placeholders: Map<String, Any>

    data class FinishOnboardingBasecamp(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.FINISH_ONBOARDING_BASECAMP

        override val subject: String = "\uD83D\uDC99 Vítejte v Basecampu – Váš první krok k výsledkům právě začíná"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class FinishOnboardingMasterclass(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.FINISH_ONBOARDING_MASTERCLASS

        override val subject: String =
            "\uD83D\uDC99 Vítejte ve Funded Mind – právě začíná Vaše nová tradingová kapitola"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class OtpEmail(
        private val userName: String,
        private val code: String,
        private val expiresAt: Instant,
    ) : AppEmail() {
        init {
            require(code.length == 4) { "Code must be exactly 4 characters long." }
        }

        override val type: EmailType
            get() = EmailType.OTP_EMAIL

        override val subject: String = "Ověřte své přihlášení do Funded Mind"

        override val placeholders = mapOf(
            "name" to userName,
            "otp_expiration_time" to expiresAt.formatToCzechDateTime(),
            "otp_char_1" to code[0],
            "otp_char_2" to code[1],
            "otp_char_3" to code[2],
            "otp_char_4" to code[3],
        )
    }

    data class StudentFinishesMentoringCheckout(
        private val studentFirstNameVocative: String,
        private val traderFullName: String,
        private val productName: String,
        private val productAltDescription: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.STUDENT_FINISH_MENTORING_CHECKOUT

        override val subject: String =
            "\uD83E\uDDD1\u200D\uD83C\uDFEB Vítejte v mentoringu! Další krok k výsledkům právě začíná"

        override val placeholders = mapOf(
            "student_name" to studentFirstNameVocative,
            "mentor_name" to traderFullName,
        )
    }

    data class TraderFinishesMentoringCheckout(
        private val productName: String,
        private val studentFullName: String,
        private val studentEmail: String,
        private val studentPhone: String?,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.TRADER_FINISH_MENTORING_CHECKOUT

        override val subject: String = "Nový student na 1:1 mentoring - $studentFullName"

        override val placeholders = mapOf(
            "product_name" to productName,
            "full_name" to studentFullName,
            "email" to studentEmail,
            "phone" to (studentPhone ?: "Číslo není známo"),
        )
    }

    data class DiscordFinishedCheckoutConnect(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.DISCORD_FINISH_CHECKOUT_CONNECT

        override val subject: String = "\uD83D\uDC65 Vítejte v placené komunitě Funded Mind!"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class ExclusiveUpgraded(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.EXCLUSIVE_UPGRADED

        override val subject: String = "\uD83E\uDD29 Vítejte ve Funded Mind Exclusive"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class DiscordCancelled(
        private val studentFirstNameVocative: String,
        private val endsAt: Instant,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.DISCORD_CANCELLED

        override val subject: String = "Potvrzení o zrušení členství na Discordu FM!"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
            "end_date" to endsAt.formatToCzechDateTime(),
        )
    }

    data class DiscordEnded(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.DISCORD_ENDED

        override val subject: String = "\uD83D\uDE22 Vaše členství v komunitě Funded Mind bylo ukončeno"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class DiscordPaymentFailed(
        private val studentFirstNameVocative: String,
        private val endsAt: Instant,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.DISCORD_PAYMENT_FAILED

        override val subject: String = "\uD83D\uDC40 Nepodařilo se obnovit Vaše členství ve Funded Mind"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
            "end_date" to endsAt.formatToCzechDateTime(),
        )
    }

    data class CommentThreadReaction(
        private val studentName: String,
        private val lessonUrl: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.COMMENT_THREAD_REACTION

        override val subject: String = "💬 Nová odpověď ve vlákně pod videem"

        override val placeholders = mapOf(
            "name" to studentName,
            "lesson_url" to lessonUrl,
        )
    }

    data class NetworkingDirectMessage(
        private val senderUserName: String,
        private val recipientUserName: String,
        private val message: String,
        private val messageUrl: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.NETWORKING_DIRECT_MESSAGE

        override val subject: String = "$senderUserName ti posílá zprávu! Připoj se na Discord ⚡ a odpověz."

        override val placeholders = mapOf(
            "sender_name" to senderUserName,
            "recipient_name" to recipientUserName,
            "message" to message,
            "message_url" to messageUrl,
        )
    }

    data class GameDocumentDenied(
        private val studentFirstNameVocative: String,
        private val denyMessage: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.GAME_DOCUMENT_DENIED

        override val subject: String = "Váš dokument nebyl schválen! \uD83D\uDE22"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
            "deny_message" to denyMessage,
        )
    }

    data class GameDocumentCertificateApproved(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.GAME_DOCUMENT_CERTIFICATE_APPROVED

        override val subject: String = "Váš certifikát byl schválen! \uD83C\uDF89"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class GameDocumentBacktestingApproved(
        private val studentFirstNameVocative: String,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.GAME_DOCUMENT_BACKTESTING_APPROVED

        override val subject: String = "Váš backtesting byl schválen! \uD83C\uDF89"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
        )
    }

    data class GameDocumentPayoutApproved(
        private val studentFirstNameVocative: String,
        private val gameLevel: GameLevel,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.GAME_DOCUMENT_PAYOUT_APPROVED

        override val subject: String = "Váš payout byl schválen! \uD83C\uDF89"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
            "game_level" to gameLevel.name,
        )
    }
    data class GameDocumentPayoutProgressApproved(
        private val studentFirstNameVocative: String,
        private val gameLevel: GameLevel,
        private val progressAmount: BigDecimal,
    ) : AppEmail() {
        override val type: EmailType
            get() = EmailType.GAME_DOCUMENT_PAYOUT_PROGRESS_APPROVED

        override val subject: String = "Váš payout byl schválen! \uD83C\uDF89"

        override val placeholders = mapOf(
            "name" to studentFirstNameVocative,
            "game_level" to gameLevel.name,
            "progress_amount" to progressAmount.toString(),
        )
    }
}
