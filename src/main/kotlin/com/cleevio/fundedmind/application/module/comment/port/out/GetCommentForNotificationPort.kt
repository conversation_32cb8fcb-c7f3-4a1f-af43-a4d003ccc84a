package com.cleevio.fundedmind.application.module.comment.port.out

import java.util.UUID

interface GetCommentForNotificationPort {
    operator fun invoke(commentId: UUID): GetCommentForNotification

    data class GetCommentForNotification(
        val commentId: UUID,
        val text: String,
        val threadId: UUID?,
        val authorName: String,
        val authorAppUserId: UUID,
        val lessonId: UUID,
        val lessonName: String,
        val courseModuleId: UUID,
        val courseModuleName: String,
        val courseId: UUID,
        val courseName: String,
    )
}
