package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.course.command.ReorderCoursesByCategoryCommand
import com.cleevio.fundedmind.application.module.course.finder.CourseFinderService
import com.cleevio.fundedmind.domain.course.exception.ActiveCoursesMismatchException
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ReorderCoursesByCategoryCommandHandler(
    private val courseFinderService: CourseFinderService,
    private val lockService: LockService,
) : CommandHandler<Unit, ReorderCoursesByCategoryCommand> {

    override val command = ReorderCoursesByCategoryCommand::class

    @Transactional
    override fun handle(command: ReorderCoursesByCategoryCommand) {
        checkAllCoursesOfCategoryAreProvided(command)

        command.courseOrderings.forEach { (courseId, newListingOrder) ->
            lockService.obtainBlockingLock(
                module = Locks.Course.MODULE,
                lockName = Locks.Course.UPDATE,
                /* params = */
                courseId.toString(),
            ).use {
                courseFinderService
                    .getByIdAndCourseCategoryNonDeleted(courseId, command.courseCategory)
                    .updateListingOrder(newListingOrder)
            }
        }
    }

    private fun checkAllCoursesOfCategoryAreProvided(command: ReorderCoursesByCategoryCommand) {
        val coursesInCategoryCount = courseFinderService.countByCategoryNonDeleted(command.courseCategory)
        if (coursesInCategoryCount != command.courseOrderings.size.toLong()) {
            throw ActiveCoursesMismatchException(
                "Cannot reorder courses because provided courses do not reflect non-deleted courses of given category.",
            )
        }
    }
}
