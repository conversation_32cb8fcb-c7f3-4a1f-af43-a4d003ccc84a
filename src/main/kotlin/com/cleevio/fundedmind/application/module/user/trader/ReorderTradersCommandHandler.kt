package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.trader.command.ReorderTradersCommand
import com.cleevio.fundedmind.application.module.user.trader.exception.ActiveTradersMismatchException
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ReorderTradersCommandHandler(
    private val traderFinderService: TraderFinderService,
    private val lockService: LockService,
) : CommandHandler<Unit, ReorderTradersCommand> {

    override val command = ReorderTradersCommand::class

    @Transactional
    override fun handle(command: ReorderTradersCommand) {
        checkAllTradersAreProvided(command)

        command.traderOrderings.forEach { (traderId, newListingOrder) ->
            lockService.obtainBlockingLock(
                module = Locks.Trader.MODULE,
                lockName = Locks.Trader.UPDATE,
                /* params = */
                traderId.toString(),
            ).use {
                traderFinderService
                    .getById(traderId)
                    .updateListingOrder(newListingOrder)
            }
        }
    }

    private fun checkAllTradersAreProvided(command: ReorderTradersCommand) {
        val tradersCount = traderFinderService.countAll()
        if (tradersCount != command.traderOrderings.size.toLong()) {
            throw ActiveTradersMismatchException(
                "Cannot reorder traders because provided traders do not reflect active traders.",
            )
        }
    }
}
