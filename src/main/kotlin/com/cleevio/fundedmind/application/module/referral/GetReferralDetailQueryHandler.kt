package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.referral.finder.ReferralFinderService
import com.cleevio.fundedmind.application.module.referral.query.GetReferralDetailQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetReferralDetailQueryHandler(
    private val referralFinderService: ReferralFinderService,
    private val appFileFinderService: AppFileFinderService,
) : QueryHandler<GetReferralDetailQuery.Result, GetReferralDetailQuery> {

    override val query = GetReferralDetailQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetReferralDetailQuery): GetReferralDetailQuery.Result {
        val referral = referralFinderService.getById(query.referralId)

        return GetReferralDetailQuery.Result(
            referralId = referral.id,
            listingOrder = referral.listingOrder,
            published = referral.published,
            imageDesktop = referral.imageDesktopFileId?.let { appFileFinderService.getImageById(it) },
            imageMobile = referral.imageMobileFileId?.let { appFileFinderService.getImageById(it) },
            title = referral.title,
            description = referral.description,
            visibleToTiers = referral.visibleToTiers,
            visibleToDiscordUsers = referral.visibleToDiscordUsers,
            linkUrl = referral.linkUrl,
            rewardCouponCode = referral.rewardCouponCode,
        )
    }
}
