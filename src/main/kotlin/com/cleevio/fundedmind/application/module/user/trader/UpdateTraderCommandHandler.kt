package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.application.module.user.trader.command.UpdateTraderCommand
import com.cleevio.fundedmind.application.module.user.trader.event.TraderUpdatedEvent
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderCalendlyUserUriAlreadyTakenException
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateTraderCommandHandler(
    private val traderFinderService: TraderFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateTraderCommand> {

    override val command = UpdateTraderCommand::class

    @Transactional
    @Lock(module = Locks.Trader.MODULE, lockName = Locks.Trader.UPDATE)
    override fun handle(@LockFieldParameter("traderId") command: UpdateTraderCommand) {
        traderFinderService
            .getById(command.traderId)
            .apply {
                if (command.calendly != null && command.calendly.calendlyUrl != this.calendlyUrl) {
                    checkNewCalendlyUserUriNotTaken(command.calendly.calendlyUserUri)
                }
            }
            .apply {
                update(
                    position = command.position,
                    firstName = command.firstName,
                    lastName = command.lastName,
                    biography = command.biography,
                    tags = command.tags,
                    badgeColor = command.badgeColor,
                    commentControl = command.commentControl,
                    socialLinkInstagram = command.socialLinkInstagram,
                    socialLinkLinkedin = command.socialLinkLinkedin,
                    socialLinkFacebook = command.socialLinkFacebook,
                    socialLinkTwitter = command.socialLinkTwitter,
                    calendlyUrl = command.calendly?.calendlyUrl,
                    calendlyUserUri = command.calendly?.calendlyUserUri,
                    checkoutVideoUrl = command.checkoutVideoUrl,
                    mentoringAvailability = command.mentoringAvailability,
                )
            }
            .also { trader ->
                applicationEventPublisher.publishEvent(
                    TraderUpdatedEvent(traderId = trader.id),
                )
            }
    }

    private fun checkNewCalendlyUserUriNotTaken(calendlyUserUri: CalendlyUserUri) {
        if (traderFinderService.existsByCalendlyUserUri(calendlyUserUri = calendlyUserUri)) {
            throw TraderCalendlyUserUriAlreadyTakenException(
                "Trader already registered with Calendly user uri: '$calendlyUserUri'.",
            )
        }
    }
}
