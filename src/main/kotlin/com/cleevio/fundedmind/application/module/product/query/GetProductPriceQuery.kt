package com.cleevio.fundedmind.application.module.product.query

import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.application.common.type.StripeProductId
import io.swagger.v3.oas.annotations.media.Schema

data class GetProductPriceQuery(
    val stripeProductId: StripeProductId,
) : Query<GetProductPriceQuery.Result> {

    @Schema(name = "GetProductPriceResult")
    data class Result(
        val price: MoneyResult,
    )
}
