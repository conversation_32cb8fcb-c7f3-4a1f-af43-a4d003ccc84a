package com.cleevio.fundedmind.application.module.user.appuser.query

import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class GetUserQuery(
    val userId: UUID,
) : Query<GetUserQuery.Result> {

    @Schema(name = "GetUserResult")
    data class Result(
        val userId: UUID,
        val createdAt: Instant,
        val email: String,
        val role: UserRole,
        val onboardingFinished: Boolean,
    )
}

data class GetInvoicesQuery(
    val userId: UUID,
) : Query<GetInvoicesQuery.Result> {

    @Schema(name = "GetInvoicesResult")
    data class Result(
        val invoices: List<GetInvoicesInvoice>,
    )

    data class GetInvoicesInvoice(
        val productName: String,
        val invoicedAt: Instant,
        val moneyResult: MoneyResult,
        val paid: <PERSON><PERSON><PERSON>,
        val linkUrl: String,
    )
}
