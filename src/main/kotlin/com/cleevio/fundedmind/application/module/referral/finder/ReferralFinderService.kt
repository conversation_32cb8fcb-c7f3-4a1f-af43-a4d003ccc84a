package com.cleevio.fundedmind.application.module.referral.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.referral.Referral
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.domain.referral.exception.ReferralNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class ReferralFinderService(
    private val referralRepository: ReferralRepository,
) : BaseFinderService<Referral>(referralRepository) {

    override fun errorBlock(message: String) = throw ReferralNotFoundException(message)

    override fun getEntityType() = Referral::class

    fun findMaxListingOrderNonDeleted(): Int? = referralRepository.findMaxListingOrderNonDeleted()

    fun findAllNonDeleted(): List<Referral> = referralRepository.findAllByDeletedAtIsNull()

    fun countNonDeleted(): Long = referralRepository.findAllByDeletedAtIsNull().size.toLong()
}
