package com.cleevio.fundedmind.application.module.user.student.event

import java.time.Instant
import java.util.UUID

data class StudentTierUpdatedInAppEvent(
    val studentId: UUID,
)

data class StudentTierUpdatedFromHubspotEvent(
    val studentId: UUID,
)

data class StudentProfileUpdatedEvent(
    val studentId: UUID,
)

data class StudentDiscordSubscriptionActivatedEvent(
    val studentId: UUID,
)

data class StudentDiscordSubscriptionEndedEvent(
    val studentId: UUID,
)

data class StudentDiscordSubscriptionCancelIntentEvent(
    val studentId: UUID,
    val subscriptionEndsAt: Instant,
)

data class StudentDiscordSubscriptionPaymentFailedEvent(
    val studentId: UUID,
    val subscriptionEndsAt: Instant,
)

data class StudentDiscordSubscriptionUpdatedEvent(
    val studentId: UUID,
)

data class StudentConnectedDiscordEvent(
    val studentDiscordId: UUID,
)
data class StudentDisconnectedDiscordEvent(
    val studentDiscordId: UUID,
)

data class StudentQuestionnaireUpdatedEvent(
    val studentId: UUID,
)

data class StudentPrivacySettingsUpdatedEvent(
    val studentId: UUID,
)

data class StudentLocationUpdatedEvent(
    val studentId: UUID,
)
