package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.course.port.out.SearchCoursesPort
import com.cleevio.fundedmind.application.module.course.query.SearchCoursesQuery
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SearchCoursesQueryHandler(
    val searchCoursesPort: SearchCoursesPort,
) : QueryHandler<InfiniteScrollSlice<SearchCoursesQuery.Result, UUID>, SearchCoursesQuery> {

    override val query = SearchCoursesQuery::class

    override fun handle(query: SearchCoursesQuery): InfiniteScrollSlice<SearchCoursesQuery.Result, UUID> =
        searchCoursesPort(
            filter = query.filter,
            infiniteScroll = query.infiniteScroll,
        )
}
