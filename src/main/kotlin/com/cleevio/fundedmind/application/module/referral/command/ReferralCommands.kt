package com.cleevio.fundedmind.application.module.referral.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import jakarta.validation.Valid
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class CreateNewReferralCommand(
    @field:NullOrNotBlankAndLimited val title: String?,
    @field:NullOrNotBlankAndLimited val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val linkUrl: String?,
    @field:NullOrNotBlankAndLimited val rewardCouponCode: String?,
) : Command<IdResult>

data class UpdateReferralCommand(
    val referralId: UUID,
    @field:NullOrNotBlankAndLimited val title: String?,
    @field:NullOrNotBlankAndLimited val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val linkUrl: String?,
    @field:NullOrNotBlankAndLimited val rewardCouponCode: String?,
) : Command<Unit>

data class DeleteReferralCommand(
    val referralId: UUID,
) : Command<Unit>

data class PublishReferralCommand(
    val referralId: UUID,
) : Command<Unit>

data class HideReferralCommand(
    val referralId: UUID,
) : Command<Unit>

data class ReorderReferralsCommand(
    val referralOrderings: List<@Valid ReferralOrderingInput>,
) : Command<Unit> {
    data class ReferralOrderingInput(
        val referralId: UUID,
        @field:PositiveOrZero val newListingOrder: Int,
    )
}
