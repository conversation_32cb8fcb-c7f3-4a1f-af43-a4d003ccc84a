package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.appuser.command.SendVerificationCodeCommand
import com.cleevio.fundedmind.application.module.user.appuser.event.VerificationCodeCreatedEvent
import com.cleevio.fundedmind.application.module.user.appuser.exception.VerificationCodeAlreadyExistsException
import com.cleevio.fundedmind.application.module.user.appuser.finder.VerificationCodeFinderService
import com.cleevio.fundedmind.domain.user.appuser.CreateVerificationCodeService
import com.cleevio.fundedmind.domain.user.appuser.constant.VerificationCodeStatus
import com.cleevio.fundedmind.infrastructure.properties.VerificationCodeProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Component
class SendVerificationCodeCommandHandler(
    private val createVerificationCodeService: CreateVerificationCodeService,
    private val verificationCodeProperties: VerificationCodeProperties,
    private val verificationCodeFinderService: VerificationCodeFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, SendVerificationCodeCommand> {

    override val command = SendVerificationCodeCommand::class

    @Transactional
    @Lock(module = Locks.AppUser.MODULE, lockName = Locks.AppUser.CREATE)
    override fun handle(@LockFieldParameter("userId") command: SendVerificationCodeCommand) {
        checkVerificationCodeIsUniquePerUser(command)

        verificationCodeFinderService
            .findAllByAppUserId(command.userId)
            .filter { it.status == VerificationCodeStatus.VALID }
            .forEach { it.expire() }

        val expiresAt = Instant.now().plus(verificationCodeProperties.expiration)

        val createdCode = createVerificationCodeService.createVerificationCode(
            appUserId = command.userId,
            code = command.code,
            expiresAt = expiresAt,
        )

        applicationEventPublisher.publishEvent(
            VerificationCodeCreatedEvent(createdCode.id),
        )
    }

    private fun checkVerificationCodeIsUniquePerUser(command: SendVerificationCodeCommand) {
        if (verificationCodeFinderService.existsByCodeAndAppUserId(command.code, command.userId)) {
            throw VerificationCodeAlreadyExistsException(
                "User: ${command.userId} already has a verification code: ${command.code}",
            )
        }
    }
}
