package com.cleevio.fundedmind.application.module.highlight.event

import java.util.UUID

data class HighlightCreatedEvent(
    val highlightId: UUID,
)

data class HighlightUpdatedEvent(
    val highlightId: UUID,
)

data class HighlightDeletedEvent(
    val highlightId: UUID,
)

data class HighlightPublishedEvent(
    val highlightId: UUID,
)

data class HighlightHiddenEvent(
    val highlightId: UUID,
)
