package com.cleevio.fundedmind.application.module.course.query

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class SearchCoursesQuery(
    @field:Valid val infiniteScroll: InfiniteScroll<UUID>,
    @field:Valid val filter: Filter,
) : Query<InfiniteScrollSlice<SearchCoursesQuery.Result, UUID>> {

    data class Filter(
        override val searchString: String?,
    ) : AutocompleteFilter

    @Schema(name = "SearchCoursesResult")
    data class Result(
        val courseId: UUID,
        val traderBio: TraderBio,
        val title: String,
        val courseCategory: CourseCategory,
        val moduleCount: Int,
        val lessonCount: Int,
        val published: Boolean,
    )

    @Schema(name = "SearchCoursesTraderBio")
    data class TraderBio(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
    )
}
