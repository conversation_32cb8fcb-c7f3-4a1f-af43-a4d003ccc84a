package com.cleevio.fundedmind.application.module.notification.event.listener

import com.cleevio.fundedmind.application.module.comment.event.CommentCreatedEvent
import com.cleevio.fundedmind.application.module.comment.port.out.GetCommentForNotificationPort
import com.cleevio.fundedmind.application.module.notification.port.out.SendNotificationMessagePort
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class LessonNotificationEventListener(
    private val sendNotificationMessagePort: SendNotificationMessagePort,
    private val getCommentForNotificationPort: GetCommentForNotificationPort,
) {

    @SentryTransaction(operation = "async.notification.comment-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleCommentCreatedEvent(event: CommentCreatedEvent) {
        val comment = getCommentForNotificationPort(event.commentId)

        sendNotificationMessagePort.newCommentUnderLesson(
            commentId = comment.commentId,
            commentText = comment.text,
            commentOwnerName = comment.authorName,
            threadId = comment.threadId,
            lessonId = comment.lessonId,
            lessonName = comment.lessonName,
            courseModuleId = comment.courseModuleId,
            courseModuleName = comment.courseModuleName,
            courseId = comment.courseId,
            courseName = comment.courseName,
        )
    }
}
