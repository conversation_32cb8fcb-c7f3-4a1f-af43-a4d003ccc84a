package com.cleevio.fundedmind.application.common.constants

object Locks {
    object AppUser {
        const val MODULE = "USER_MODULE"
        const val CREATE = "CREATE_USER"
        const val UPDATE = "UPDATE_USER"
    }

    object Onboarding {
        const val MODULE = "ONBOARDING_MODULE"
        const val UPDATE = "UPDATE_ONBOARDING"
    }

    object Student {
        const val MODULE = "STUDENT_MODULE"
        const val CREATE = "CREATE_STUDENT"
        const val UPDATE = "UPDATE_STUDENT"
    }

    object Trader {
        const val MODULE = "TRADER_MODULE"
        const val CREATE = "CREATE_TRADER"
        const val UPDATE = "UPDATE_TRADER"
    }

    object Product {
        const val MODULE = "PRODUCT_MODULE"
        const val CREATE = "CREATE_PRODUCT"
        const val UPDATE = "UPDATE_PRODUCT"
    }

    object Meeting {
        const val MODULE = "MEETING_MODULE"
        const val CREATE = "CREATE_MEETING"
        const val UPDATE = "UPDATE_MEETING"
    }

    object Highlight {
        const val MODULE = "HIGHLIGHT_MODULE"
        const val CREATE = "CREATE_HIGHLIGHT"
        const val UPDATE = "UPDATE_HIGHLIGHT"
    }

    object Course {
        const val MODULE = "COURSE_MODULE"
        const val CREATE = "CREATE_COURSE"
        const val UPDATE = "UPDATE_COURSE"
    }

    object CourseModule {
        const val MODULE = "COURSE_MODULE_MODULE"
        const val CREATE = "CREATE_COURSE_MODULE"
        const val UPDATE = "UPDATE_COURSE_MODULE"
    }

    object Lesson {
        const val MODULE = "LESSON_MODULE"
        const val CREATE = "CREATE_LESSON"
        const val UPDATE = "UPDATE_LESSON"
    }

    object Progress {
        const val MODULE = "PROGRESS_MODULE"
        const val UPDATE = "UPDATE_PROGRESS"
    }

    object File {
        const val MODULE = "FILE_MODULE"
        const val UPDATE = "UPDATE_FILE"
    }

    object Comment {
        const val MODULE = "COMMENT_MODULE"
        const val CREATE = "CREATE_COMMENT"
        const val UPDATE = "UPDATE_COMMENT"
    }

    object Referral {
        const val MODULE = "REFERRAL_MODULE"
        const val CREATE = "CREATE_REFERRAL"
        const val UPDATE = "UPDATE_REFERRAL"
    }

    object MentoringMeeting {
        const val MODULE = "MENTORING_MEETING_MODULE"
        const val UPDATE = "UPDATE_MENTORING_MEETING"
    }

    object Payment {
        const val MODULE = "PAYMENT_MODULE"
        const val INVOICE_GENERATION = "INVOICE_GENERATION"
    }

    object GameLevelReward {
        const val MODULE = "GAME_LEVEL_REWARD_MODULE"
        const val CREATE = "CREATE_GAME_LEVEL_REWARD"
        const val UPDATE = "UPDATE_GAME_LEVEL_REWARD"
    }

    object GameDocument {
        const val MODULE = "GAME_DOCUMENT_MODULE"
        const val CREATE = "CREATE_GAME_DOCUMENT"
        const val UPDATE = "UPDATE_GAME_DOCUMENT"
    }
}
