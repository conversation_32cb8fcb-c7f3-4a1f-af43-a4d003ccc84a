package com.cleevio.fundedmind.application.common.port.out

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.GeoLocationValue
import com.cleevio.fundedmind.application.module.networking.query.GetPeopleInNetworkingQuery
import com.cleevio.fundedmind.application.module.networking.query.GetUserContactQuery
import com.cleevio.fundedmind.application.module.networking.query.ListPeopleInNetworkingQuery
import com.cleevio.fundedmind.application.module.networking.query.NetworkingPerson
import com.cleevio.fundedmind.application.module.networking.query.SearchPeopleInNetworkingQuery
import java.util.UUID

interface GetPeopleCountInNetworkingPort {
    operator fun invoke(userId: UUID): GetPeopleInNetworkingQuery.Result
}

interface GetNetworkingPeoplePort {
    fun searchPeople(
        meUserId: UUID,
        filter: SearchPeopleInNetworkingQuery.Filter,
        infiniteScroll: InfiniteScroll<UUID>,
        includeLastNameMapper: () -> Boolean,
        includeObfuscatedLocation: () -> Boolean,
    ): InfiniteScrollSlice<SearchPeopleInNetworkingQuery.Result, UUID>

    fun listPeopleByLocation(
        userLocationIds: List<UUID>,
        filter: ListPeopleInNetworkingQuery.Filter,
        limit: Int,
        includeLastNameMapper: () -> Boolean,
        includeObfuscatedLocation: () -> Boolean,
    ): List<NetworkingPerson>
}

interface ListUserLocationsPort {
    operator fun invoke(
        northEast: GeoLocationInput,
        southWest: GeoLocationInput,
    ): List<ListUserLocationsResult>

    data class ListUserLocationsResult(
        val userLocationId: UUID,
        val obfuscatedLocation: GeoLocationValue,
    )
}

interface GetUserContactPort {
    fun getById(userId: UUID): GetUserContactQuery.Result
}
