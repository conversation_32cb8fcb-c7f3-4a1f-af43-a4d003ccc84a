package com.cleevio.fundedmind.application.common.command

import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.domain.common.AppButton
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color

data class AppButtonInput(
    @field:NotBlankAndLimited val text: String,
    val color: Color,
) {
    fun toDomainButton() = AppButton(
        text = text,
        color = color,
    )
}

data class AppButtonResult(
    val text: String,
    val color: Color,
)

data class AppButtonWithLinkInput(
    @field:NotBlankAndLimited val text: String,
    val color: Color,
    @field:NotBlankAndLimited val linkUrl: String,
) {
    fun toDomainButton() = AppButtonWithLink(
        text = text,
        color = color,
        linkUrl = linkUrl,
    )
}

data class AppButtonWithLinkResult(
    val text: String,
    val color: Color,
    val linkUrl: String,
)
