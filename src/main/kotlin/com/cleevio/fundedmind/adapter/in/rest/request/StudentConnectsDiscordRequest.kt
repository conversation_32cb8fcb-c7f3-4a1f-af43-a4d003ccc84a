package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.student.command.StudentConnectsDiscordCommand
import java.util.UUID

data class StudentConnectsDiscordRequest(
    val discordUserId: String,
    val discordUserName: String,
    val accessToken: String,
) {
    fun toCommand(studentId: UUID) = StudentConnectsDiscordCommand(
        userId = studentId,
        discordUserId = discordUserId,
        discordUserName = discordUserName,
        accessToken = accessToken,
    )
}
