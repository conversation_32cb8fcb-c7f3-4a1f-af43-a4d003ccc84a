package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.comment.command.CreateNewCommentCommand
import java.util.UUID

data class CreateCommentRequest(
    val text: String,
    val threadId: UUID? = null,
) {
    fun toCommand(
        lessonId: UUID,
        appUserId: UUID,
    ) = CreateNewCommentCommand(
        appUserId = appUserId,
        lessonId = lessonId,
        text = text,
        threadId = threadId,
    )
}

data class UpdateCommentRequest(
    val text: String,
)
