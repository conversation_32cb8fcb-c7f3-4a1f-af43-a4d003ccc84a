package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateNewLessonRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ListLessonsInCourseModuleRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ReorderLessonsInCourseModuleRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.UpdateLessonRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.lesson.command.DeleteLessonCommand
import com.cleevio.fundedmind.application.module.lesson.query.GetLessonDetailQuery
import com.cleevio.fundedmind.application.module.lesson.query.ListLessonsInCourseModuleQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Lesson [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/courses/{courseId}/modules/{courseModuleId}/lessons")
class LessonController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/{lessonId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getLessonDetail(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @PathVariable lessonId: UUID,
    ): GetLessonDetailQuery.Result = queryBus(
        GetLessonDetailQuery(
            courseId = courseId,
            courseModuleId = courseModuleId,
            lessonId = lessonId,
        ),
    )

    @Operation(
        description = """
            Admin creates new lesson in course module.
            400 - module does not belong to course
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createLesson(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @RequestBody request: CreateNewLessonRequest,
    ): IdResult = commandBus(
        request.toCommand(
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )

    @Operation(
        description = """
            Admin updates a lesson in course module.
            400 - module does not belong to course
            400 - lesson is not related to provided module
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/{lessonId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateLesson(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @PathVariable lessonId: UUID,
        @RequestBody request: UpdateLessonRequest,
    ): Unit = commandBus(
        request.toCommand(
            courseId = courseId,
            courseModuleId = courseModuleId,
            lessonId = lessonId,
        ),
    )

    @Operation(
        description = """
            Admin deletes a lesson.
            400 - module does not belong to course
            400 - lesson is not related to provided module
            404 - Lesson not found
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @DeleteMapping("/{lessonId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteLesson(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @PathVariable lessonId: UUID,
    ): Unit = commandBus(
        DeleteLessonCommand(
            courseId = courseId,
            courseModuleId = courseModuleId,
            lessonId = lessonId,
        ),
    )

    @Operation(
        description = """
            Admin lists all lessons in course module (Drag&Drop table).
            Search fields are: 'title'
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/list", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listLessonsInCourseModule(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @RequestBody request: ListLessonsInCourseModuleRequest,
    ): ListLessonsInCourseModuleQuery.Result = queryBus(
        request.toQuery(
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )

    @Operation(
        description = """
            Admin changes display order of lessons in course module (Drag&Drop table).
            400 - module does not belong to course
            400 - display order is not positive or zero
            422 - payload does not contain all lessons of the course module
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/reorder", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun reorderLessonsInCourseModule(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @RequestBody request: ReorderLessonsInCourseModuleRequest,
    ): Unit = commandBus(request.toCommand(courseId, courseModuleId))
}
