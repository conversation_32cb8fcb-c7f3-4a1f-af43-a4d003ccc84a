package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateNewCourseModuleRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ListCourseModulesInCourseRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ReorderCourseModulesInCourseRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.UpdateCourseModuleRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.coursemodule.command.DeleteCourseModuleCommand
import com.cleevio.fundedmind.application.module.coursemodule.query.GetCourseModuleDetailQuery
import com.cleevio.fundedmind.application.module.coursemodule.query.ListCourseModulesInCourseQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "CourseModule [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/courses/{courseId}/modules")
class CourseModuleController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/{courseModuleId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getCourseModuleDetail(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
    ): GetCourseModuleDetailQuery.Result = queryBus(
        GetCourseModuleDetailQuery(
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )

    @Operation(
        description = """
            Admin creates new course module.
            400 - both reward coupon and button are provided
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createCourseModule(
        @PathVariable courseId: UUID,
        @RequestBody request: CreateNewCourseModuleRequest,
    ): IdResult = commandBus(request.toCommand(courseId))

    @Operation(
        description = """
            Admin updates a course module.
            400 - both reward coupon and button are provided
            400 - module is not related to provided course
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/{courseModuleId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateCourseModule(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @RequestBody request: UpdateCourseModuleRequest,
    ): Unit = commandBus(
        request.toCommand(
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )

    @Operation(
        description = """
            Admin deletes a course module.
            404 - CourseModule not found
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @DeleteMapping("/{courseModuleId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteCourseModule(
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
    ): Unit = commandBus(
        DeleteCourseModuleCommand(
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )

    @Operation(
        description = """
            Admin lists all modules in course (Drag&Drop table).
            Search fields are: 'title'
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/list", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listCourseModulesInCourse(
        @PathVariable courseId: UUID,
        @RequestBody request: ListCourseModulesInCourseRequest,
    ): ListCourseModulesInCourseQuery.Result = queryBus(request.toQuery(courseId))

    @Operation(
        description = """
            Admin changes display order of modules in course (Drag&Drop table).
            400 - display order is not positive or zero
            422 - payload does not contain all course modules of the course
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/reorder", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun reorderCourseModulesInCourse(
        @PathVariable courseId: UUID,
        @RequestBody request: ReorderCourseModulesInCourseRequest,
    ): Unit = commandBus(request.toCommand(courseId))
}
