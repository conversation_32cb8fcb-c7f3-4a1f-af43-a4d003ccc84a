package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.ListPublishedCoursesByCategoryRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.course.command.UserDownloadsCourseAttachmentCommand
import com.cleevio.fundedmind.application.module.course.query.GetCourseEducationalPillarsQuery
import com.cleevio.fundedmind.application.module.course.query.GetPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListAttachmentsOfPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListModulesOfPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListPublishedCoursesByCategoryQuery
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Course [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/courses/me")
class CourseMeController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {

    @Operation(
        description = """
            User gets overview of educational pillars.
	    """,
    )
    @GetMapping("/pillars", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getEducationalPillars(@AuthenticationPrincipal userId: UUID): GetCourseEducationalPillarsQuery.Result =
        queryBus(GetCourseEducationalPillarsQuery(userId = userId))

    @Operation(
        description = """
            User gets published courses by id.
            Course is locked for student if their tier is not matching allowed tiers of the course.
            404 - course is not published or found or it is deleted
	    """,
    )
    @GetMapping("/{courseId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getPublishedCourse(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
    ): GetPublishedCourseQuery.Result = queryBus(GetPublishedCourseQuery(userId = userId, courseId = courseId))

    @Operation(
        description = """
            User lists published courses by category.
            Courses are locked for student if their tier is not matching allowed tiers of the course.
            Unpublished/hidden courses are filtered out.
	    """,
    )
    @PostMapping("/list/category", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listPublishedCoursesByCategory(
        @AuthenticationPrincipal userId: UUID,
        @RequestBody request: ListPublishedCoursesByCategoryRequest,
    ): ListPublishedCoursesByCategoryQuery.Result = queryBus(request.toQuery(userId))

    @Operation(
        description = """
            User gets modules of published course.
	    """,
    )
    @GetMapping("/{courseId}/modules", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listModulesOfPublishedCourse(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
    ): ListModulesOfPublishedCourseQuery.Result = queryBus(
        ListModulesOfPublishedCourseQuery(
            userId = userId,
            courseId = courseId,
        ),
    )

    @Operation(
        description = """
            User gets attachments of published course.
            403 - course is locked for student and they are not allowed to see attachments
	    """,
    )
    @GetMapping("/{courseId}/attachments", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listAttachmentsOfPublishedCourse(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
    ): ListAttachmentsOfPublishedCourseQuery.Result = queryBus(
        ListAttachmentsOfPublishedCourseQuery(
            userId = userId,
            courseId = courseId,
        ),
    )

    @Operation(
        description = """
            User downloads a lesson attachment (they get a temporary URL).
            400 - file is not a document (FileType)
            403 - course is locked for student and they are not allowed to see (and download) attachments
        """,
    )
    @GetMapping("/{courseId}/attachments/{attachmentId}/download", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun userDownloadsAttachment(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
        @PathVariable attachmentId: UUID,
    ): UserDownloadsCourseAttachmentCommand.Result = commandBus(
        UserDownloadsCourseAttachmentCommand(
            userId = userId,
            courseId = courseId,
            attachmentId = attachmentId,
        ),
    )
}
