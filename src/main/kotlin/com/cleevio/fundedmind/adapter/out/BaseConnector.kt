package com.cleevio.fundedmind.adapter.out

import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.http.HttpEntity
import org.springframework.http.HttpStatusCode
import org.springframework.http.MediaType
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.web.client.RestClient
import org.springframework.web.client.toEntity
import java.io.BufferedReader
import java.time.Duration

abstract class BaseConnector(
    baseUrl: String,
    restClientCustomizer: (RestClient.Builder) -> Unit = {},
) {
    protected val logger = logger()

    protected val restClient = RestClient
        .builder()
        .baseUrl(baseUrl)
        .requestFactory(
            HttpComponentsClientHttpRequestFactory()
                .apply {
                    setConnectTimeout(DEFAULT_CONNECT_TIMEOUT)
                    setReadTimeout(DEFAULT_READ_TIMEOUT)
                },
        )
        .requestInterceptor(
            LoggingInterceptor(this.javaClass.simpleName),
        )
        .also(restClientCustomizer)
        .build()

    protected open val errorStatusPredicate: (HttpStatusCode) -> Boolean = { !it.is2xxSuccessful }

    protected fun <T : RestClient.RequestHeadersSpec<T>> RestClient.RequestHeadersUriSpec<T>.uriAndHeaders(
        path: String,
        queryParams: Map<String, Collection<*>> = mapOf(),
        accept: MediaType = MediaType.APPLICATION_JSON,
    ): T = uri {
        it.path(path)
        queryParams.forEach { (key, value) -> it.queryParam(key, value.joinToString(separator = ",")) }
        it.build()
    }
        .accept(accept)

    protected fun RestClient.RequestBodySpec.jsonBody(body: Any): RestClient.RequestBodySpec = body(body)
        .contentType(MediaType.APPLICATION_JSON)

    protected fun RestClient.ResponseSpec.errorStatusHandler(
        statusPredicate: (HttpStatusCode) -> Boolean = errorStatusPredicate,
    ): RestClient.ResponseSpec = onStatus(statusPredicate, errorHandler)

    protected inline fun <reified T : Any> RestClient.RequestHeadersSpec<*>.retrieveResponseWithCustomErrorHandler(
        noinline statusPredicate: (HttpStatusCode) -> Boolean = { !it.is2xxSuccessful },
        errorHandler: RestClient.ResponseSpec.ErrorHandler,
    ): T? = retrieve()
        .onStatus(statusPredicate, errorHandler)
        .toEntity<T>()
        .getBodyOrThrow()

    protected inline fun <reified T : Any> RestClient.RequestHeadersSpec<*>.retrieveResponseWithErrorHandler(
        excludedStatusCode: HttpStatusCode,
    ): T? = try {
        retrieve()
            .errorStatusHandler()
            .toEntity<T>()
            .body
    } catch (e: IntegrationException) {
        if (e.statusCode == excludedStatusCode.value()) null else throw e
    }

    protected inline fun <reified T : Any> RestClient.RequestHeadersSpec<*>.retrieveResponseWithErrorHandler(): T =
        retrieve()
            .errorStatusHandler()
            .toEntity<T>()
            .getBodyOrThrow()

    protected fun RestClient.RequestHeadersSpec<*>.retrieveWithCustomErrorHandler(
        statusPredicate: (HttpStatusCode) -> Boolean = { !it.is2xxSuccessful },
        errorHandler: RestClient.ResponseSpec.ErrorHandler,
    ) {
        retrieve()
            .onStatus(statusPredicate, errorHandler)
            .toBodilessEntity()
    }

    protected fun RestClient.RequestHeadersSpec<*>.retrieveWithErrorHandler() {
        retrieve()
            .errorStatusHandler()
            .toBodilessEntity()
    }

    protected fun <T> HttpEntity<T>.getBodyOrThrow(): T =
        body ?: error("${this.javaClass.simpleName} could not parse response body")

    protected fun <T> executeWithRetry(
        retryCount: Int = 2,
        retryOnStatusCode: Int,
        retryLogic: (Throwable) -> Unit = {},
        logic: () -> T,
    ): T {
        var attempts = 0
        while (true) {
            try {
                return logic()
            } catch (ex: IntegrationException) {
                val shouldRetry = ex.statusCode == retryOnStatusCode
                if (++attempts >= retryCount || !shouldRetry) throw ex
                retryLogic(ex)
            }
        }
    }

    private val errorHandler = RestClient.ResponseSpec.ErrorHandler { request, response ->
        val body = BufferedReader(response.body.reader()).readLines().joinToString(separator = " ")
        logger.warn(
            """
            An error code occurred while calling ${request.method} ${request.uri}.
            Response [code: ${response.statusCode}, body: $body]
            """.trimIndent(),
        )

        throw IntegrationException(
            statusCode = response.statusCode.value(),
            responseBody = body,
            message = "Error ${response.statusCode} - ${request.method} ${request.uri}",
        )
    }
}

private val DEFAULT_CONNECT_TIMEOUT = Duration.ofSeconds(10)
private val DEFAULT_READ_TIMEOUT = Duration.ofSeconds(15)
