package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.course.port.out.ListCoursesByCategoryPort
import com.cleevio.fundedmind.application.module.course.query.ListCoursesByCategoryQuery
import com.cleevio.fundedmind.jooq.tables.references.COURSE
import com.cleevio.fundedmind.jooq.tables.references.COURSE_MODULE
import com.cleevio.fundedmind.jooq.tables.references.LESSON
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.selectCount
import org.springframework.stereotype.Component

@Component
class ListCoursesByCategoryJooq(
    private val dslContext: DSLContext,
) : ListCoursesByCategoryPort {

    override fun invoke(filter: ListCoursesByCategoryQuery.Filter): ListCoursesByCategoryQuery.Result {
        val conditions = listOfNotNull(
            COURSE.DELETED_AT.isNull(),
            COURSE.COURSE_CATEGORY.eq(filter.courseCategory),
            filter.autocompleteCondition(
                COURSE.TITLE,
                COURSE.trader.FIRST_NAME,
                COURSE.trader.LAST_NAME,
            ),
        )

        return dslContext
            .select(
                COURSE.ID,
                COURSE.TITLE,
                COURSE.COURSE_CATEGORY,
                COURSE.LISTING_ORDER,
                COURSE.PUBLISHED,
                modulesCount,
                lessonsCount,
                COURSE.trader.ID,
                COURSE.trader.POSITION,
                COURSE.trader.FIRST_NAME,
                COURSE.trader.LAST_NAME,
                COURSE.trader.traderProfilePicture.ID,
                COURSE.trader.traderProfilePicture.ORIGINAL_FILE_URL,
                COURSE.trader.traderProfilePicture.COMPRESSED_FILE_URL,
                COURSE.trader.traderProfilePicture.BLUR_HASH,
                COURSE.trader.BADGE_COLOR,
            )
            .from(COURSE)
            .where(conditions)
            .orderBy(COURSE.LISTING_ORDER.asc())
            .fetch()
            .map(::mapSearchCourses)
            .let { ListCoursesByCategoryQuery.Result(data = it) }
    }

    private val modulesCount = field(
        selectCount()
            .from(COURSE_MODULE)
            .where(COURSE_MODULE.COURSE_ID.eq(COURSE.ID))
            .and(COURSE_MODULE.DELETED_AT.isNull()),
    ).`as`("modules_count")

    private val lessonsCount = field(
        selectCount()
            .from(LESSON)
            .innerJoin(COURSE_MODULE).on(LESSON.COURSE_MODULE_ID.eq(COURSE_MODULE.ID))
            .where(COURSE_MODULE.COURSE_ID.eq(COURSE.ID))
            .and(COURSE_MODULE.DELETED_AT.isNull())
            .and(LESSON.DELETED_AT.isNull()),
    ).`as`("lessons_count")

    private fun mapSearchCourses(record: Record) = ListCoursesByCategoryQuery.CourseListing(
        courseId = record[COURSE.ID]!!,
        title = record[COURSE.TITLE]!!,
        courseCategory = record[COURSE.COURSE_CATEGORY]!!,
        listingOrder = record[COURSE.LISTING_ORDER]!!,
        moduleCount = record[modulesCount] ?: 0,
        lessonCount = record[lessonsCount] ?: 0,
        published = record[COURSE.PUBLISHED]!!,
        traderBio = ListCoursesByCategoryQuery.TraderBio(
            traderId = record[COURSE.trader.ID]!!,
            position = record[COURSE.trader.POSITION]!!,
            firstName = record[COURSE.trader.FIRST_NAME]!!,
            lastName = record[COURSE.trader.LAST_NAME]!!,
            profilePicture = record[COURSE.trader.traderProfilePicture.ID]?.let { imageId ->
                ImageResult(
                    imageId = imageId,
                    imageOriginalUrl = record[COURSE.trader.traderProfilePicture.ORIGINAL_FILE_URL]!!,
                    imageCompressedUrl = record[COURSE.trader.traderProfilePicture.COMPRESSED_FILE_URL]!!,
                    imageBlurHash = record[COURSE.trader.traderProfilePicture.BLUR_HASH]!!,
                )
            },
            badgeColor = record[COURSE.trader.BADGE_COLOR]!!,
        ),
    )
}
