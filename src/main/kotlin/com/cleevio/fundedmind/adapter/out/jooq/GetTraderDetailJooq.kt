package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.port.out.ProductIdentifiersToPriceMapMapper
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.application.module.user.trader.port.out.GetTraderDetailPort
import com.cleevio.fundedmind.application.module.user.trader.query.GetTraderDetailQuery
import com.cleevio.fundedmind.application.module.user.trader.query.GetTraderDetailQuery.TraderProductBio
import com.cleevio.fundedmind.jooq.tables.references.APP_USER
import com.cleevio.fundedmind.jooq.tables.references.PRODUCT
import com.cleevio.fundedmind.jooq.tables.references.TRADER
import org.jooq.DSLContext
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetTraderDetailJooq(private val dslContext: DSLContext) : GetTraderDetailPort {

    override fun invoke(
        traderId: UUID,
        pricesMapper: ProductIdentifiersToPriceMapMapper,
    ): GetTraderDetailQuery.Result {
        val conditions = listOfNotNull(
            TRADER.ID.eq(traderId),
        )

        return dslContext
            .select(
                TRADER.ID,
                APP_USER.ACCOUNT_ACTIVE,
                TRADER.POSITION,
                TRADER.FIRST_NAME,
                TRADER.LAST_NAME,
                TRADER.PHONE,
                TRADER.BIOGRAPHY,
                TRADER.COUNTRY,
                TRADER.TAGS,
                TRADER.BADGE_COLOR,
                TRADER.COMMENT_CONTROL,
                TRADER.SOCIAL_LINK_INSTAGRAM,
                TRADER.SOCIAL_LINK_LINKEDIN,
                TRADER.SOCIAL_LINK_FACEBOOK,
                TRADER.SOCIAL_LINK_TWITTER,
                TRADER.CALENDLY_URL,
                TRADER.CALENDLY_USER_URI,
                TRADER.CHECKOUT_VIDEO_URL,
                TRADER.MENTORING_AVAILABILITY,
                TRADER.traderProfilePicture.ID,
                TRADER.traderProfilePicture.ORIGINAL_FILE_URL,
                TRADER.traderProfilePicture.COMPRESSED_FILE_URL,
                TRADER.traderProfilePicture.BLUR_HASH,
                TRADER.introPicture.ID,
                TRADER.introPicture.ORIGINAL_FILE_URL,
                TRADER.introPicture.COMPRESSED_FILE_URL,
                TRADER.introPicture.BLUR_HASH,
                TRADER.mentoringPhoto.ID,
                TRADER.mentoringPhoto.ORIGINAL_FILE_URL,
                TRADER.mentoringPhoto.COMPRESSED_FILE_URL,
                TRADER.mentoringPhoto.BLUR_HASH,
                saleableTraderProducts,
            )
            .from(TRADER)
            .innerJoin(APP_USER).on(TRADER.ID.eq(APP_USER.ID))
            .where(conditions)
            .fetchOne()
            ?.map {
                val priceMap = it[saleableTraderProducts]
                    .map { it[PRODUCT.STRIPE_IDENTIFIER]!! }
                    .let { pricesMapper(it) }

                GetTraderDetailQuery.Result(
                    traderId = it[TRADER.ID]!!,
                    active = it[APP_USER.ACCOUNT_ACTIVE]!!,
                    position = it[TRADER.POSITION]!!,
                    firstName = it[TRADER.FIRST_NAME]!!,
                    lastName = it[TRADER.LAST_NAME]!!,
                    phone = it[TRADER.PHONE],
                    biography = it[TRADER.BIOGRAPHY],
                    country = it[TRADER.COUNTRY]!!,
                    tags = it[TRADER.TAGS]!!,
                    badgeColor = it[TRADER.BADGE_COLOR]!!,
                    commentControl = it[TRADER.COMMENT_CONTROL]!!,
                    socialLinkInstagram = it[TRADER.SOCIAL_LINK_INSTAGRAM],
                    socialLinkLinkedin = it[TRADER.SOCIAL_LINK_LINKEDIN],
                    socialLinkFacebook = it[TRADER.SOCIAL_LINK_FACEBOOK],
                    socialLinkTwitter = it[TRADER.SOCIAL_LINK_TWITTER],
                    calendly = run {
                        val url = it[TRADER.CALENDLY_URL]
                        val userUri = it[TRADER.CALENDLY_USER_URI]

                        if (url != null && userUri != null) {
                            GetTraderDetailQuery.TraderCalendly(
                                calendlyUrl = url,
                                calendlyUserUri = userUri,
                            )
                        } else {
                            null
                        }
                    },
                    checkoutVideoUrl = it[TRADER.CHECKOUT_VIDEO_URL],
                    profilePicture = it[TRADER.traderProfilePicture.ID]?.let { imageId ->
                        ImageResult(
                            imageId = imageId,
                            imageOriginalUrl = it[TRADER.traderProfilePicture.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = it[TRADER.traderProfilePicture.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = it[TRADER.traderProfilePicture.BLUR_HASH]!!,
                        )
                    },
                    introPicture = it[TRADER.introPicture.ID]?.let { imageId ->
                        ImageResult(
                            imageId = imageId,
                            imageOriginalUrl = it[TRADER.introPicture.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = it[TRADER.introPicture.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = it[TRADER.introPicture.BLUR_HASH]!!,
                        )
                    },
                    mentoringPhoto = it[TRADER.mentoringPhoto.ID]?.let { imageId ->
                        ImageResult(
                            imageId = imageId,
                            imageOriginalUrl = it[TRADER.mentoringPhoto.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = it[TRADER.mentoringPhoto.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = it[TRADER.mentoringPhoto.BLUR_HASH]!!,
                        )
                    },
                    traderProducts = it[saleableTraderProducts].map { record ->
                        TraderProductBio(
                            productId = record[PRODUCT.ID]!!,
                            productName = record[PRODUCT.NAME]!!,
                            saleable = record[PRODUCT.SALEABLE]!!,
                            price = priceMap.get(record[PRODUCT.STRIPE_IDENTIFIER]!!),
                        )
                    },
                    mentoringAvailability = it[TRADER.MENTORING_AVAILABILITY]!!,
                )
            }
            ?: throw TraderNotFoundException("Trader: '$traderId' not found.")
    }

    private val saleableTraderProducts = multiset(
        select(
            PRODUCT.ID,
            PRODUCT.STRIPE_IDENTIFIER,
            PRODUCT.NAME,
            PRODUCT.SALEABLE,
        )
            .from(PRODUCT)
            .where(
                PRODUCT.TRADER_ID.eq(TRADER.ID),
                PRODUCT.DELETED_AT.isNull(),
            )
            .orderBy(PRODUCT.SALEABLE.desc(), PRODUCT.NAME.asc()),
    )
}
