package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.port.out.GetAppUserForInternalPort
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserNotFoundException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.jooq.tables.references.APP_USER
import com.cleevio.fundedmind.jooq.tables.references.STUDENT
import com.cleevio.fundedmind.jooq.tables.references.TRADER
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetAppUserForInternalJooq(private val dslContext: DSLContext) : GetAppUserForInternalPort {

    override fun invoke(userId: UUID): GetAppUserForInternalPort.GetAppUserForInternalResult = dslContext
        .select(
            APP_USER.ID,
            APP_USER.ROLE,
            STUDENT.ID,
            STUDENT.NETWORKING_VISIBILITY,
            STUDENT.DISCORD_SUBSCRIPTION,
            TRADER.NETWORKING_VISIBILITY,
        )
        .from(APP_USER)
        .leftJoin(STUDENT).on(APP_USER.ID.eq(STUDENT.ID))
        .leftJoin(TRADER).on(APP_USER.ID.eq(TRADER.ID))
        .where(APP_USER.ID.eq(userId))
        .fetchOne()
        ?.map {
            val role = it[APP_USER.ROLE]!!

            GetAppUserForInternalPort.GetAppUserForInternalResult(
                userId = it[APP_USER.ID]!!,
                role = role,
                networkingVisibility = when (role) {
                    UserRole.STUDENT -> it[STUDENT.NETWORKING_VISIBILITY]
                    UserRole.TRADER -> it[TRADER.NETWORKING_VISIBILITY]
                    UserRole.ADMIN -> null
                },
                discordSubscription = when (role) {
                    UserRole.STUDENT -> it[STUDENT.DISCORD_SUBSCRIPTION]
                    UserRole.TRADER -> null
                    UserRole.ADMIN -> null
                },
            )
        }
        ?: throw UserNotFoundException("User '$userId' not found.")
}
