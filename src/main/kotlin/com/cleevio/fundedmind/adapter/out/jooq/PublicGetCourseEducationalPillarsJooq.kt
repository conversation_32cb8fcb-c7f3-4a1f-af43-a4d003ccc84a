package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.course.port.out.PublicGetCourseEducationalPillarsPort
import com.cleevio.fundedmind.application.module.course.query.PublicGetCourseEducationalPillarsQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.jooq.tables.references.COURSE
import org.jooq.DSLContext
import org.jooq.impl.DSL.count
import org.springframework.stereotype.Component

@Component
class PublicGetCourseEducationalPillarsJooq(
    private val dslContext: DSLContext,
) : PublicGetCourseEducationalPillarsPort {

    override fun invoke(): PublicGetCourseEducationalPillarsQuery.Result {
        // Fetch course counts per category
        val courseCounts = dslContext
            .select(COURSE.COURSE_CATEGORY, count().`as`("course_count_of_category"))
            .from(COURSE)
            .where(
                COURSE.DELETED_AT.isNull,
                COURSE.PUBLISHED.isTrue,
                COURSE.PUBLIC.isTrue,
            )
            .groupBy(COURSE.COURSE_CATEGORY)
            .fetch()
            .associate {
                val category = it[COURSE.COURSE_CATEGORY]!!
                val count = it["course_count_of_category"] as? Int ?: 0

                category to count
            }

        // Fetch first course id per category
        val firstCourseIds = dslContext
            .select(COURSE.COURSE_CATEGORY, COURSE.ID)
            .from(COURSE)
            .where(
                COURSE.DELETED_AT.isNull,
                COURSE.PUBLISHED.isTrue,
                COURSE.PUBLIC.isTrue,
            )
            .orderBy(COURSE.COURSE_CATEGORY.asc(), COURSE.CREATED_AT.asc())
            .fetchGroups(COURSE.COURSE_CATEGORY, COURSE.ID)
            .mapValues { it.value.firstOrNull() }

        val pillars = CourseCategory.entries.associateWith { category ->
            val courseCount = courseCounts[category] ?: 0L
            val firstCourseId = firstCourseIds[category]
            PublicGetCourseEducationalPillarsQuery.CourseEducationalPillar(
                courseCategory = category,
                hasSingleCourse = courseCount == 1,
                firstCourseId = firstCourseId,
            )
        }

        return PublicGetCourseEducationalPillarsQuery.Result(pillars = pillars)
    }
}
