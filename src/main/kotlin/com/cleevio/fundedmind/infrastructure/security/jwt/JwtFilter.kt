package com.cleevio.fundedmind.infrastructure.security.jwt

import com.cleevio.fundedmind.application.common.port.out.ParseTokenPort
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.domain.user.appuser.AppUser
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.filter.OncePerRequestFilter

class JwtFilter(
    private val parseTokenPort: ParseTokenPort,
    private val firebaseUserFinderService: AppUserFinderService,
    private val publicEndpointsMatcher: RequestMatcher,
) : OncePerRequestFilter() {

    // we don't need this filter if auth is already set from other filters (e.g. JwtSignUpFilter/StripeSignatureFilter)
    // or if the path is a public endpoint
    override fun shouldNotFilter(request: HttpServletRequest): Boolean {
        val alreadyAuthenticated = SecurityContextHolder.getContext().authentication != null
        val isPublicEndpoint = publicEndpointsMatcher.matches(request)

        return alreadyAuthenticated || isPublicEndpoint
    }

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) = getAuthentication(request)
        ?.let { it: UsernamePasswordAuthenticationToken -> SecurityContextHolder.getContext().authentication = it }
        .run { filterChain.doFilter(request, response) }

    private fun getAuthentication(request: HttpServletRequest): UsernamePasswordAuthenticationToken? = runCatching {
        getTokenFromRequest(request)
            .let { token: String -> parseTokenPort.parseToken(token = token) }
            .let { authDetail -> firebaseUserFinderService.getByFirebaseIdentifier(authDetail.firebaseIdentifier) }
            .let { user: AppUser ->
                val authority = SimpleGrantedAuthority("ROLE_${user.role.name}")
                UsernamePasswordAuthenticationToken(user.id, null, setOf(authority))
            }
    }.onFailure { exception ->
        logger.warn("Failed to authenticate request: ${request.method} ${request.requestURI}: ${exception.message}")
    }.getOrNull()
}
