package com.cleevio.fundedmind.infrastructure.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import java.util.Optional
import java.util.UUID

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
class JPAAuditConfig {

    @Bean
    fun auditorProvider(): AuditorAware<UUID> = AuditorAware {
        SecurityContextHolder.getContext().authentication
            ?.takeIf { auth -> auth.isAuthenticated }
            ?.takeIf { auth -> auth is UsernamePasswordAuthenticationToken }
            ?.let { auth -> Optional.ofNullable(auth.principal as? UUID) }
            ?: Optional.empty()
    }
}
