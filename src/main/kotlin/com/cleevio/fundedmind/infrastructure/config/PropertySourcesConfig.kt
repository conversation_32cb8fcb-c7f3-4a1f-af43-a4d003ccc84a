package com.cleevio.fundedmind.infrastructure.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer
import org.springframework.core.io.ClassPathResource

@Configuration
class PropertySourcesConfig {
    @Bean
    fun placeholderConfigurer() = PropertySourcesPlaceholderConfigurer().apply {
        setLocation(ClassPathResource("git.properties"))
        setIgnoreResourceNotFound(true)
        setIgnoreUnresolvablePlaceholders(true)
    }
}
