package com.cleevio.fundedmind.infrastructure.config

import com.cleevio.fundedmind.application.common.port.out.ParseTokenPort
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.infrastructure.properties.FundedMindSecurityProperties
import com.cleevio.fundedmind.infrastructure.properties.WebhookSecurityProperties
import com.cleevio.fundedmind.infrastructure.security.CalendlyWebhookFilter
import com.cleevio.fundedmind.infrastructure.security.HubspotWebhookFilter
import com.cleevio.fundedmind.infrastructure.security.RequestLoggingFilter
import com.cleevio.fundedmind.infrastructure.security.StripeSignatureFilter
import com.cleevio.fundedmind.infrastructure.security.UserMdcFilter
import com.cleevio.fundedmind.infrastructure.security.apikey.ApiKeyValidator
import com.cleevio.fundedmind.infrastructure.security.apikey.FundedMindApiKeyFilter
import com.cleevio.fundedmind.infrastructure.security.jwt.JwtFilter
import com.cleevio.fundedmind.infrastructure.security.jwt.JwtSignUpFilter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher
import org.springframework.security.web.util.matcher.OrRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
class SecurityConfig(
    private val appUserFinderService: AppUserFinderService,
    private val parseTokenPort: ParseTokenPort,
    private val webhookSecurityProperties: WebhookSecurityProperties,
    private val fundedMindSecurityProperties: FundedMindSecurityProperties,
) {

    @Bean
    fun corsFilter(): CorsConfigurationSource {
        val config = CorsConfiguration().apply {
            allowCredentials = true
            allowedOriginPatterns = listOf(CorsConfiguration.ALL)
            allowedHeaders = listOf(CorsConfiguration.ALL)
            allowedMethods = listOf(CorsConfiguration.ALL)
        }

        return UrlBasedCorsConfigurationSource().apply {
            registerCorsConfiguration("/**", config)
        }
    }

    private val publicEndpointsMatcher: RequestMatcher = OrRequestMatcher(
        PATH_PATTERN.matcher("/public/**"),
        PATH_PATTERN.matcher("/api-docs/**"),
        PATH_PATTERN.matcher("/actuator/**"),
        PATH_PATTERN.matcher(HttpMethod.GET, "/health"),
    )

    @Bean
    fun filterChain(http: HttpSecurity): SecurityFilterChain = http
        .csrf { it.disable() }
        .cors { it.configurationSource(corsFilter()) }
        .authorizeHttpRequests {
            it.requestMatchers(publicEndpointsMatcher).permitAll()
            it.requestMatchers(STRIPE_WEBHOOK_EVENTS_MATCHER).permitAll()
            it.requestMatchers(HUBSPOT_WEBHOOK_EVENTS_MATCHER).permitAll()
            it.requestMatchers(CALENDLY_WEBHOOK_EVENTS_MATCHER).permitAll()
            it.requestMatchers(ADMIN_API_KEY_MATCHER).permitAll()
            it.anyRequest().authenticated()
        }
        .exceptionHandling { it.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }
        .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
        .addFilterBefore(
            StripeSignatureFilter(
                requestMatcher = STRIPE_WEBHOOK_EVENTS_MATCHER,
                secretKey = webhookSecurityProperties.stripe.secretKey,
            ),
            UsernamePasswordAuthenticationFilter::class.java,
        )
        .addFilterBefore(
            HubspotWebhookFilter(
                requestMatcher = HUBSPOT_WEBHOOK_EVENTS_MATCHER,
                secretKey = webhookSecurityProperties.hubspot.secretKey,
            ),
            UsernamePasswordAuthenticationFilter::class.java,
        )
        .addFilterBefore(
            CalendlyWebhookFilter(
                requestMatcher = CALENDLY_WEBHOOK_EVENTS_MATCHER,
                secretKey = webhookSecurityProperties.calendly.secretKey,
            ),
            UsernamePasswordAuthenticationFilter::class.java,
        )
        .addFilterBefore(
            FundedMindApiKeyFilter(
                requestMatcher = ADMIN_API_KEY_MATCHER,
                apiKeyValidator = ApiKeyValidator(supportedApiKey = fundedMindSecurityProperties.apiKey),
            ),
            UsernamePasswordAuthenticationFilter::class.java,
        )
        .addFilterAfter(
            JwtSignUpFilter(
                parseTokenPort = parseTokenPort,
                requestMatcher = PATH_PATTERN.matcher("/users/sign-up"),
            ),
            BasicAuthenticationFilter::class.java,
        )
        .addFilterAfter(
            JwtFilter(
                parseTokenPort = parseTokenPort,
                firebaseUserFinderService = appUserFinderService,
                publicEndpointsMatcher = publicEndpointsMatcher,
            ),
            JwtSignUpFilter::class.java,
        )
        .addFilterAfter(
            UserMdcFilter(),
            JwtFilter::class.java,
        )
        .also {
            if (fundedMindSecurityProperties.requestLogging.enabled) {
                it.addFilterAfter(
                    RequestLoggingFilter(),
                    UserMdcFilter::class.java,
                )
            }
        }
        .build()
}

private val PATH_PATTERN: PathPatternRequestMatcher.Builder = PathPatternRequestMatcher.withDefaults()

private val STRIPE_WEBHOOK_EVENTS_MATCHER =
    PATH_PATTERN.matcher(HttpMethod.POST, "/webhooks/stripe/events")

private val HUBSPOT_WEBHOOK_EVENTS_MATCHER =
    PATH_PATTERN.matcher(HttpMethod.POST, "/hubspot/**")

private val CALENDLY_WEBHOOK_EVENTS_MATCHER =
    PATH_PATTERN.matcher(HttpMethod.POST, "/webhooks/calendly")

private val ADMIN_API_KEY_MATCHER = PATH_PATTERN.matcher("/admin/**")
