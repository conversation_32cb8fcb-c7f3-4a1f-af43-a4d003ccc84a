package com.cleevio.fundedmind.infrastructure.middleware

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.CommandHandler
import jakarta.validation.Valid
import org.springframework.stereotype.Component
import org.springframework.validation.annotation.Validated
import kotlin.reflect.KClass

@Component
@Validated
class CommandBusMiddleware(commandHandlers: List<CommandHandler<*, *>>) : CommandBus {
    private val handlers: MutableMap<String, CommandHandler<*, Command<*>>> = mutableMapOf()

    init {
        commandHandlers.forEach {
            if (handlers.containsKey(it.command.commandName())) {
                throw Exception("Multiple handlers for single command ${it.command.commandName()}")
            }
            @Suppress("UNCHECKED_CAST")
            handlers[it.command.commandName()] = it as CommandHandler<*, Command<*>>
        }
    }

    override fun <R> invoke(@Valid command: Command<R>): R {
        val commandName = command::class.commandName()

        if (!handlers.containsKey(commandName)) throw Exception("No handler for command $commandName")
        val handler = handlers[commandName]!!

        @Suppress("UNCHECKED_CAST")
        return handler.handle(command) as R
    }
}

private fun <T : Any> KClass<T>.commandName(): String = toString()
