package com.cleevio.fundedmind.infrastructure.properties

import jakarta.validation.Valid
import jakarta.validation.constraints.PositiveOrZero
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated

@ConfigurationProperties(prefix = "fundedmind.location")
@Validated
data class UserLocationProperties(
    @field:Valid val obfuscation: ObfuscationProperties,
) {
    data class ObfuscationProperties(
        @field:PositiveOrZero val minRadiusMeters: Double,
        @field:PositiveOrZero val maxRadiusMeters: Double,
    ) {
        init {
            require(maxRadiusMeters >= minRadiusMeters) {
                "Maximum radius (${maxRadiusMeters}m) must be greater than or equal " +
                    "to minimum radius (${minRadiusMeters}m)"
            }
        }
    }
}
