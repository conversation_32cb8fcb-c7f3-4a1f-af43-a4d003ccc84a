CREATE TABLE app_user
(
    id         UUID PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    created_by <PERSON><PERSON><PERSON>,
    updated_at TIMESTAMPTZ NOT NULL,
    updated_by U<PERSON><PERSON>,
    email      TEXT        NOT NULL,
    role       TEXT        NOT NULL
);

CREATE UNIQUE INDEX "app_user_email_ui" ON "app_user" (email);

CREATE TABLE app_file
(
    id         UUID PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    created_by U<PERSON><PERSON>,
    extension  TEXT        NOT NULL,
    type       TEXT        NOT NULL
);
