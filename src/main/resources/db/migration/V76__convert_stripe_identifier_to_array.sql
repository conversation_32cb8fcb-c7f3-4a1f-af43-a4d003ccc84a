-- Convert stripe_identifier from TEXT to TEXT[] in app_user table

-- Step 1: Add a new column for the array
ALTER TABLE app_user
ADD COLUMN stripe_identifiers TEXT[];

-- Step 2: Copy existing data to the new column as arrays
-- For non-null values, convert to single-element array
-- For null values, set to empty array
UPDATE app_user
SET stripe_identifiers = 
    CASE 
        WHEN stripe_identifier IS NOT NULL AND stripe_identifier != '' 
        THEN ARRAY[stripe_identifier]
        ELSE '{}'::TEXT[]
    END;

-- Step 3: Drop the old column
ALTER TABLE app_user
DROP COLUMN stripe_identifier;