package com.cleevio.fundedmind.domain.networking

import com.cleevio.fundedmind.application.common.util.UUIDv7
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.util.UUID

class NetworkingMessageTest {

    private val senderUserId: UUID = UUIDv7.randomUUID()
    private val recipientUserId: UUID = UUIDv7.randomUUID()

    @Test
    fun `textShortened - should return original text when 10 or fewer words and under 200 characters`() {
        // given
        val text = "This is a short message with exactly ten words here."
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe text
    }

    @Test
    fun `textShortened - should return first 10 words with ellipsis when more than 10 words`() {
        // given
        val text = "This is a message with more than ten words so it should be shortened properly with ellipsis"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe "This is a message with more than ten words so..."
    }

    @Test
    fun `textShortened - should handle spam with one very long word`() {
        // given
        val longWord = "a".repeat(250) // 250 characters, no spaces
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = longWord,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe longWord.take(200) + "..."
        result.length shouldBe 203
    }

    @Test
    fun `textShortened - should handle spam with two words where one is very long`() {
        // given
        val longWord = "a".repeat(250)
        val text = "Hello $longWord"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe text.take(200) + "..."
        result.length shouldBe 203
    }

    @Test
    fun `textShortened - should handle multiple words that together exceed 200 characters`() {
        // given
        val word = "verylongword".repeat(10) // Each word is 120 chars
        val text = "$word $word" // Two words, total > 200 chars
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe text.take(200) + "..."
        result.length shouldBe 203
    }

    @Test
    fun `textShortened - should handle exactly 10 words under 200 characters`() {
        // given
        val text = "One two three four five six seven eight nine ten"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe text
    }

    @Test
    fun `textShortened - should handle exactly 11 words`() {
        // given
        val text = "One two three four five six seven eight nine ten eleven"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe "One two three four five six seven eight nine ten..."
    }

    @Test
    fun `textShortened - should handle empty text`() {
        // given
        val text = ""
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe ""
    }

    @Test
    fun `textShortened - should handle single word under 200 characters`() {
        // given
        val text = "Hello"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe "Hello"
    }

    @Test
    fun `textShortened - should handle text with multiple spaces`() {
        // given
        val text = "Hello    world    this    has    multiple    spaces"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe "Hello    world    this    has    multiple    spaces"
    }

    @Test
    fun `textShortened - should handle text with newlines and tabs`() {
        // given
        val text = "Hello\nworld\tthis\nhas\tvarious\nwhitespace\tcharacters"
        val message = NetworkingMessage.create(
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        )

        // when
        val result = message.textShortened

        // then
        result shouldBe "Hello\nworld\tthis\nhas\tvarious\nwhitespace\tcharacters"
    }
}
