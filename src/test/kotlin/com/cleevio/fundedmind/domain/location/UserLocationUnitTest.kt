package com.cleevio.fundedmind.domain.location

import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class UserLocationUnitTest {

    @Test
    fun `formattedAddress - should return formatted address with all fields`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = "Main Street 123",
            city = "Prague",
            postalCode = "017 01",
            state = "Czech Republic",
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "Main Street 123, 017 01 Prague, Czech Republic"
    }

    @Test
    fun `formattedAddress - should return formatted address without street`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = null,
            city = "Prague",
            postalCode = "017 01",
            state = "Czech Republic",
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "017 01 Prague, Czech Republic"
    }

    @Test
    fun `formattedAddress - should return formatted address without postal code`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = "Main Street 123",
            city = "Prague",
            postalCode = null,
            state = "Czech Republic",
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "Main Street 123, Prague, Czech Republic"
    }

    @Test
    fun `formattedAddress - should return formatted address without city`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = "Main Street 123",
            city = null,
            postalCode = "017 01",
            state = "Czech Republic",
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "Main Street 123, 017 01, Czech Republic"
    }

    @Test
    fun `formattedAddress - should return only street when other fields are null`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = "Main Street 123",
            city = null,
            postalCode = null,
            state = null,
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "Main Street 123"
    }

    @Test
    fun `formattedAddress - should return only state when other fields are null`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = null,
            city = null,
            postalCode = null,
            state = "Czech Republic",
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "Czech Republic"
    }

    @Test
    fun `formattedAddress - should return N-A when all fields are null`() {
        val underTest = UserLocation.newLocation(
            id = 1.toUUID(),
            street = null,
            city = null,
            postalCode = null,
            state = null,
            latitude = 50.0,
            longitude = 14.0,
            obfuscatedLatitude = 50.0,
            obfuscatedLongitude = 14.0,
        )

        underTest.formattedAddress shouldBe "N/A"
    }
}
