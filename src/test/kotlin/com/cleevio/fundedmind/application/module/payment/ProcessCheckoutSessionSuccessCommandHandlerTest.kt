package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.CheckoutSessionPort
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.module.payment.command.ProcessCheckoutSessionSuccessCommand
import com.cleevio.fundedmind.application.module.user.student.exception.CannotUpgradeStudentTierException
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.common.constant.PaymentTierState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.SubscriptionStatus
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.onboarding.OnboardingRepository
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import com.cleevio.fundedmind.truncatedShouldBe
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class ProcessCheckoutSessionSuccessCommandHandlerTest(
    @Autowired private val underTest: ProcessCheckoutSessionSuccessCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val onboardingRepository: OnboardingRepository,
    @Autowired private val mentoringRepository: MentoringRepository,
) : IntegrationTest() {

    @Test
    fun `process payment - onboarding upgrade to masterclass`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getOnboarding(id = 1.toUUID(), entityModifier = { it.upgradeToBasecamp() })

        onboardingRepository.findByIdOrNull(1.toUUID())!!.onboardingTier shouldBe OnboardingTier.BASECAMP

        every {
            hubspotService.updateCrmTier(
                hubspotIdentifier = 1,
                onboardingTier = OnboardingTier.MASTERCLASS,
            )
        } just Runs

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_MASTERCLASS",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        every {
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        } just Runs

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        onboardingRepository.findByIdOrNull(1.toUUID())!!.onboardingTier shouldBe OnboardingTier.MASTERCLASS

        verify {
            hubspotService.updateCrmTier(1, OnboardingTier.MASTERCLASS)
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        }
    }

    @Test
    fun `should pass if onboarding tier is already masterclass`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        ).also {
            dataHelper.getOnboarding(id = it.id, entityModifier = { it.upgradeToMasterclass() })
        }

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_MASTERCLASS",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        every {
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        } just Runs

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        onboardingRepository.findByIdOrNull(1.toUUID())!!.onboardingTier shouldBe OnboardingTier.MASTERCLASS

        verify {
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        }
    }

    @Test
    fun `process payment - student upgrade from basecamp to masterclass`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.BASECAMP

        every {
            hubspotService.updateCrmTier(
                hubspotIdentifier = 1,
                studentTier = StudentTier.MASTERCLASS,
            )
        } just Runs

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_MASTERCLASS",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        every {
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        } just Runs

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.MASTERCLASS

        verify {
            hubspotService.updateCrmTier(hubspotIdentifier = 1, studentTier = StudentTier.MASTERCLASS)
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.MASTERCLASS_ORGANIC_PAY)
        }
    }

    @Test
    fun `process payment - should throw if upgrading to MASTERCLASS but student is EXCLUSIVE`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE)

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_MASTERCLASS",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        shouldThrow<CannotUpgradeStudentTierException> {
            underTest.handle(
                ProcessCheckoutSessionSuccessCommand(
                    sessionIdentifier = "cs_1",
                ),
            )
        }

        verify {
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
        }
    }

    @Test
    fun `process payment - should do nothing if payment is UNKNOWN`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_123", // will determine that the payment type is UNKNOWN
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.BASECAMP
        verify {
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
        }
    }

    @Test
    fun `process payment - student upgrade from masterclass to exclusive should not upgrade tier`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS)

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.MASTERCLASS

        every { hubspotService.updateCrmPaymentTierState(1, PaymentTierState.EXCLUSIVE_SALES_WON) } just Runs

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_EXCLUSIVE",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.MASTERCLASS

        verify {
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.EXCLUSIVE_SALES_WON)
        }
    }

    @Test
    fun `should pass if student tier is already exclusive`() {
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE)

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_EXCLUSIVE",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        every {
            hubspotService.updateCrmPaymentTierState(
                hubspotIdentifier = 1,
                paymentTierState = PaymentTierState.EXCLUSIVE_SALES_WON,
            )
        } just Runs

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        studentRepository.findByIdOrNull(1.toUUID())!!.studentTier shouldBe StudentTier.EXCLUSIVE

        verify {
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
            hubspotService.updateCrmPaymentTierState(1, PaymentTierState.EXCLUSIVE_SALES_WON)
        }
    }

    @ParameterizedTest
    @CsvSource(
        nullValues = ["null"],
        value = [
            "10, 2025-01-12T00:00:00Z", // midnight 11.01. -> 12.01.
            "null, null",
        ],
    )
    fun `process payment - should create mentoring`(
        validityDays: Int?,
        expectedExpiration: String?,
    ) {
        val user = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        ).also { user ->
            dataHelper.getStudent(
                id = user.id,
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = { it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS)) },
            )
        }

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            stripeIdentifier = "prod_1",
            validityInDays = validityDays,
            sessionsCount = 5,
            name = "mentoring",
            entityModifier = { it.makeSaleable() },
        )

        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns LocalDate.of(2025, 1, 1) // 01.01.
        every { sendEmailService.sendEmailStudentFinishesMentoringCheckout(any()) } just Runs
        every { sendEmailService.sendEmailTraderFinishesMentoringCheckout(any()) } just Runs
        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_1",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        val mentoring = mentoringRepository.findAll().run {
            size shouldBe 1
            single().apply {
                productId shouldBe 1.toUUID()
                sessionIdentifier shouldBe "cs_1"
                productName shouldBe "mentoring"
                expiresAt shouldBe expectedExpiration?.toInstant()
                sessionCount shouldBe 5
                usedSessions shouldBe 0
                studentId shouldBe user.id
            }
        }

        verify {
            if (validityDays != null) {
                LocalDate.now()
            }
            sendEmailService.sendEmailStudentFinishesMentoringCheckout(mentoring.id)
            sendEmailService.sendEmailTraderFinishesMentoringCheckout(mentoring.id)
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
        }
    }

    @Test
    fun `process payment - activate discord subscription`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS)
        }

        every {
            stripeService.getSubscriptionByCheckoutSessionId("cs_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-01-01T10:00:00Z".toInstant(),
            endsAt = "2025-02-01T10:00:00Z".toInstant(),
            subscriptionCancelAt = null,
            status = SubscriptionStatus.ACTIVE,
        )

        every {
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-02-01T10:00:00Z".toInstant(),
            )
        } just Runs

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_DISCORD",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        // when
        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        // then
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            discordSubscriptionExpiresAt truncatedShouldBe "2025-02-01T10:00:00Z".toInstant()
        }

        verify {
            stripeService.getSubscriptionByCheckoutSessionId("cs_1")
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-02-01T10:00:00Z".toInstant(),
            )
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
        }
    }

    @Test
    fun `process payment - should activate discord subscription for already subscribed student`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            email = "<EMAIL>",
        )

        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.MASTERCLASS,
            entityModifier = {
                it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                it.deactivateDiscordSubscription()
            },
        )

        every {
            stripeService.getSubscriptionByCheckoutSessionId("cs_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-05-01T10:00:00Z".toInstant(),
            endsAt = "2025-06-01T10:00:00Z".toInstant(),
            status = SubscriptionStatus.ACTIVE,
            subscriptionCancelAt = null,
        )

        every {
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
        } just Runs

        every { checkoutSessionPort.getCheckoutSessionCompletedData("cs_1") } returns
            CheckoutSessionPort.CheckoutSessionCompletedData(
                checkoutSessionId = "cs_1",
                customerEmail = "<EMAIL>",
                soldProductId = "prod_DISCORD",
                customerId = "cus_1",
                paymentLinkId = null,
                subscriptionId = null,
            )

        // when
        underTest.handle(
            ProcessCheckoutSessionSuccessCommand(
                sessionIdentifier = "cs_1",
            ),
        )

        // then
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            discordSubscriptionExpiresAt!! truncatedShouldBe "2025-06-01T10:00:00Z".toInstant()
        }

        verify {
            stripeService.getSubscriptionByCheckoutSessionId("cs_1")
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
            checkoutSessionPort.getCheckoutSessionCompletedData("cs_1")
        }
    }
}
