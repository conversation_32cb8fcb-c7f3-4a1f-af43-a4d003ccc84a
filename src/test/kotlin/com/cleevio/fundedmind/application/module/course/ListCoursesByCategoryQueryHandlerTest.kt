package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.ListCoursesByCategoryQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired

class ListCoursesByCategoryQueryHandlerTest(
    @Autowired private val underTest: ListCoursesByCategoryQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list courses by category - verify mappings`() {
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        dataHelper.getCourse(
            id = 1.toUUID(),
            listingOrder = 1,
            traderId = 1.toUUID(),
            title = "Course",
            courseCategory = CourseCategory.TRADING_BASICS,
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id, entityModifier = { it.softDelete() })
            }
            // module with 1 lesson
            dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id)
            }
            // module with 1 deleted lesson
            dataHelper.getCourseModule(id = 3.toUUID(), courseId = course.id).also { module3 ->
                dataHelper.getLesson(courseModuleId = module3.id, entityModifier = { it.softDelete() })
            }
        }

        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data shouldHaveSize 1
        result.data.single().run {
            courseId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            title shouldBe "Course"
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            moduleCount shouldBe 3
            lessonCount shouldBe 3
            published shouldBe false
            traderBio.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }
        }
    }

    @Test
    fun `should list courses by category with correctly counted modules and lessons`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(id = 11.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id, entityModifier = { it.softDelete() })
            }
            // module with 1 lesson deleted
            dataHelper.getCourseModule(
                id = 12.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, entityModifier = { it.softDelete() })
            }
            // module with 1 lesson
            dataHelper.getCourseModule(id = 13.toUUID(), courseId = course.id).also { module3 ->
                dataHelper.getLesson(courseModuleId = module3.id)
            }
        }

        dataHelper.getCourse(
            // no modules
            id = 2.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.TRADING_BASICS,
        )

        dataHelper.getCourse(
            id = 3.toUUID(),
            traderId = 1.toUUID(),
            courseCategory = CourseCategory.EXCLUSIVE, // different category
        ).also { course ->
            // module with 2 lessons
            dataHelper.getCourseModule(id = 31.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
            }
            // module with 1 deleted lesson
            dataHelper.getCourseModule(
                id = 32.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            ).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id, entityModifier = { it.softDelete() })
            }
        }

        dataHelper.getCourse(
            id = 4.toUUID(),
            traderId = 2.toUUID(),
            courseCategory = CourseCategory.STRATEGY, // different category
        ).also { course ->
            dataHelper.getCourseModule(id = 41.toUUID(), courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
            }
            dataHelper.getCourseModule(id = 42.toUUID(), courseId = course.id, entityModifier = { it.softDelete() })
        }

        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data shouldHaveSize 2
        result.data.first { it.courseId == 1.toUUID() }.run {
            moduleCount shouldBe 2
            lessonCount shouldBe 3
        }
        result.data.first { it.courseId == 2.toUUID() }.run {
            moduleCount shouldBe 0
            lessonCount shouldBe 0
        }
    }

    @Test
    fun `should list courses by category with both published and hidden courses`() {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            entityModifier = { it.hide() },
        )

        // when
        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        result.data.map { it.courseId } shouldContainExactlyInAnyOrder setOf(1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list courses ordered by listing order`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), traderId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourse(id = 2.toUUID(), traderId = 1.toUUID(), listingOrder = 3)
        dataHelper.getCourse(id = 3.toUUID(), traderId = 1.toUUID(), listingOrder = 2)

        // when
        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        // then
        result.data.map { it.courseId } shouldBe listOf(1.toUUID(), 3.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list courses by category without deleted courses`() {
        // given
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            entityModifier = { it.softDelete() },
        )

        // when
        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = CourseCategory.TRADING_BASICS,
                ),
            ),
        )

        // then
        result.data.map { it.courseId } shouldBe listOf(1.toUUID())
    }

    @ParameterizedTest
    @EnumSource(CourseCategory::class)
    fun `should list courses only by provided category`(courseCategory: CourseCategory) {
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            courseCategory = CourseCategory.TRADING_BASICS,
        )
        dataHelper.getCourse(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            courseCategory = CourseCategory.BASECAMP,
        )
        dataHelper.getCourse(
            id = 3.toUUID(),
            traderId = dataHelper.getTrader(id = 3.toUUID()).id,
            courseCategory = CourseCategory.STRATEGY,
        )
        dataHelper.getCourse(
            id = 4.toUUID(),
            traderId = dataHelper.getTrader(id = 4.toUUID()).id,
            courseCategory = CourseCategory.EXCLUSIVE,
        )
        dataHelper.getCourse(
            id = 5.toUUID(),
            traderId = dataHelper.getTrader(id = 5.toUUID()).id,
            courseCategory = CourseCategory.RECORDING,
        )
        dataHelper.getCourse(
            id = 6.toUUID(),
            traderId = dataHelper.getTrader(id = 6.toUUID()).id,
            courseCategory = CourseCategory.ADD_ON,
        )

        val result = underTest.handle(
            ListCoursesByCategoryQuery(
                filter = ListCoursesByCategoryQuery.Filter(
                    searchString = null,
                    courseCategory = courseCategory,
                ),
            ),
        )

        result.data shouldHaveSize 1
        result.data.single().courseCategory shouldBe courseCategory
    }
}
