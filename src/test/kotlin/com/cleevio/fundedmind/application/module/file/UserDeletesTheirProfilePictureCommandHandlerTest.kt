package com.cleevio.fundedmind.application.module.file

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.file.command.UserDeletesTheirProfilePictureCommand
import com.cleevio.fundedmind.application.module.file.exception.ImageDeleteFailedException
import com.cleevio.fundedmind.domain.file.AppFileRepository
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.onboarding.OnboardingRepository
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class UserDeletesTheirProfilePictureCommandHandlerTest(
    @Autowired private val underTest: UserDeletesTheirProfilePictureCommandHandler,
    @Autowired private val onboardingRepository: OnboardingRepository,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val traderRepository: TraderRepository,
    @Autowired private val appFileRepository: AppFileRepository,
) : IntegrationTest() {

    @Test
    fun `should delete profile picture in onboarding`() {
        dataHelper.getOnboarding(1.toUUID(), entityModifier = {
            it.changeProfilePicture(
                fileId = dataHelper.getImage(
                    id = 0.toUUID(),
                    type = FileType.ONBOARDING_PROFILE_PICTURE,
                ).id,
            )
        })

        underTest.handle(
            UserDeletesTheirProfilePictureCommand(
                userId = 1.toUUID(),
                type = FileType.ONBOARDING_PROFILE_PICTURE,
            ),
        )

        appFileRepository.findByIdOrNull(0.toUUID()) shouldBe null
        onboardingRepository.findByIdOrNull(1.toUUID())?.profilePictureFileId shouldBe null
    }

    @Test
    fun `should delete profile picture of student`() {
        dataHelper.getStudent(1.toUUID(), entityModifier = {
            it.changeProfilePicture(
                fileId = dataHelper.getImage(
                    id = 0.toUUID(),
                    type = FileType.STUDENT_PROFILE_PICTURE,
                ).id,
            )
        })

        underTest.handle(
            UserDeletesTheirProfilePictureCommand(
                userId = 1.toUUID(),
                type = FileType.STUDENT_PROFILE_PICTURE,
            ),
        )

        appFileRepository.findByIdOrNull(0.toUUID()) shouldBe null
        studentRepository.findByIdOrNull(1.toUUID())?.profilePictureFileId shouldBe null
    }

    @Test
    fun `should delete profile picture of trader`() {
        dataHelper.getTrader(1.toUUID(), entityModifier = {
            it.changeProfilePicture(
                fileId = dataHelper.getImage(
                    id = 0.toUUID(),
                    type = FileType.TRADER_PROFILE_PICTURE,
                ).id,
            )
        })

        underTest.handle(
            UserDeletesTheirProfilePictureCommand(
                userId = 1.toUUID(),
                type = FileType.TRADER_PROFILE_PICTURE,
            ),
        )

        appFileRepository.findByIdOrNull(0.toUUID()) shouldBe null
        traderRepository.findByIdOrNull(1.toUUID())?.profilePictureFileId shouldBe null
    }

    @ParameterizedTest
    @EnumSource(
        value = FileType::class,
        names = ["ONBOARDING_PROFILE_PICTURE", "STUDENT_PROFILE_PICTURE", "TRADER_PROFILE_PICTURE"],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun `delete profile picture should throw if file type is not profile picture`(fileType: FileType) {
        shouldThrow<ImageDeleteFailedException> {
            underTest.handle(
                UserDeletesTheirProfilePictureCommand(
                    userId = 1.toUUID(),
                    type = fileType,
                ),
            )
        }
    }
}
