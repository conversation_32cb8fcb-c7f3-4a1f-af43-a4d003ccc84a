package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.GetCourseDetailQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetCourseDetailQueryHandlerTest(
    @Autowired private val underTest: GetCourseDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get course in calendar - verify mappings`() {
        // given
        dataHelper.getTrader(1.toUUID())
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            title = "Course",
            courseCategory = CourseCategory.EXCLUSIVE,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            description = "Description",
            color = Color.YELLOW,
            thumbnailUrl = "thumbnailUrl",
            thumbnailAnimationUrl = "thumbnailAnimationUrl",
            trailerUrl = "trailerUrl",
            public = true,
            homepage = false,
            entityModifier = {
                it.changeIntroPictureDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_DESKTOP_INTRO_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeIntroPictureMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MOBILE_INTRO_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            GetCourseDetailQuery(courseId = 1.toUUID()),
        )

        // then
        result.run {
            courseId shouldBe 1.toUUID()
            introPictureDesktop shouldNotBe null
            introPictureDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            introPictureMobile shouldNotBe null
            introPictureMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            title shouldBe "Course"
            courseCategory shouldBe CourseCategory.EXCLUSIVE
            visibleToTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            description shouldBe "Description"
            traderId shouldBe 1.toUUID()
            color shouldBe Color.YELLOW
            thumbnailUrl shouldBe "thumbnailUrl"
            thumbnailAnimationUrl shouldBe "thumbnailAnimationUrl"
            trailerUrl shouldBe "trailerUrl"
            published shouldBe false
            public shouldBe true
            homepage shouldBe false
        }
    }
}
