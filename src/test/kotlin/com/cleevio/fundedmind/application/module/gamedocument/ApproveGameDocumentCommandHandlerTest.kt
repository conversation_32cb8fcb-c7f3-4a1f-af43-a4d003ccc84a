package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.ApproveGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentHasWrongStateException
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDate

class ApproveGameDocumentCommandHandlerTest(
    @Autowired private val underTest: ApproveGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val gameLevelProgressRepository: GameLevelProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should approve payout and update student game level`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.FOUR)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 1_000.toBigDecimal(),
            previousLevel = GameLevel.FOUR,
            reachedLevel = GameLevel.FIVE,
            payoutDate = LocalDate.now(),
            truthScore = 80,
            scoreMessage = "Looks fine",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.FOUR
            reachedLevel shouldBe GameLevel.FIVE
            payoutAmount shouldBe 6_000.toBigDecimal()
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.FIVE

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.FIVE
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(1.toUUID()) }
    }

    @Test
    fun `should approve certificate and send certificate email`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.TWO)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
        )

        every { sendEmailService.sendEmailGameDocumentCertificateApproved(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.TWO
            reachedLevel shouldBe GameLevel.THREE
            payoutAmount shouldBe null
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.THREE

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.THREE
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentCertificateApproved(1.toUUID()) }
    }

    @Test
    fun `should approve backtesting and send backtesting email`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        // Create a game document to approve
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
        )

        every { sendEmailService.sendEmailGameDocumentBacktestingApproved(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025 10:00:00
            ),
        )

        // Verify the approval
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.APPROVED
            approvedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.TWO
            payoutAmount shouldBe null
            denyMessage shouldBe null
            deniedAt shouldBe null
        }

        // Verify student game level is updated
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.TWO

        // Verify progress entries are created
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries shouldHaveSize 1
        progressEntries.single().run {
            gameLevel shouldBe GameLevel.TWO
            shown shouldBe false
        }

        verify { sendEmailService.sendEmailGameDocumentBacktestingApproved(1.toUUID()) }
    }

    @Test
    fun `should approve game document with multiple level jumps`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.THREE)
        }

        // Create a game document to approve with multiple level jump
        val gameDocument = dataHelper.getGameDocument(
            id = 302.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 36_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SEVEN, // Jump from 3 to 7
            payoutDate = LocalDate.now(),
            truthScore = 90,
            scoreMessage = "Great progress",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify student game level is updated to the highest level
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SEVEN

        // Verify progress entries are created for all gained levels
        val progressEntries = gameLevelProgressRepository.findAllByStudentId(user.id)
        progressEntries.map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.FOUR, GameLevel.FIVE, GameLevel.SIX, GameLevel.SEVEN)

        verify { sendEmailService.sendEmailGameDocumentPayoutProgressApproved(302.toUUID()) }
    }

    @Test
    fun `should not create progress entries when current level equals reached level`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.NINE)
        }

        // Create a game document to approve with same level
        val gameDocument = dataHelper.getGameDocument(
            id = 303.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 5_000.toBigDecimal(),
            previousLevel = GameLevel.NINE,
            reachedLevel = GameLevel.NINE, // Same level
            payoutDate = LocalDate.now(),
            truthScore = 70,
            scoreMessage = "Maintaining level",
        )

        every { sendEmailService.sendEmailGameDocumentPayoutApproved(any()) } just Runs

        // Approve the game document
        underTest.handle(
            ApproveGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify student game level remains the same
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.NINE

        // Verify no progress entries are created
        gameLevelProgressRepository.findAllByStudentId(user.id).shouldBeEmpty()

        verify { sendEmailService.sendEmailGameDocumentPayoutApproved(any()) }
    }

    @Test
    fun `should throw exception when game document is not in WAITING state`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document that is already approved
        val gameDocument = dataHelper.getGameDocument(
            id = 304.toUUID(),
            studentId = user.id,
            entityModifier = { it.approve() },
        )

        // Try to approve the already approved document
        shouldThrow<GameDocumentHasWrongStateException> {
            underTest.handle(
                ApproveGameDocumentCommand(
                    gameDocumentId = gameDocument.id,
                    now = "2025-09-15T10:00:00Z".toInstant(),
                ),
            )
        }
    }
}
