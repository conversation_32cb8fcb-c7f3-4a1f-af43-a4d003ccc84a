package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserHasWrongRoleException
import com.cleevio.fundedmind.application.module.user.onboarding.exception.OnboardingNotYetFinishedException
import com.cleevio.fundedmind.application.module.user.trader.command.CreateNewTraderCommand
import com.cleevio.fundedmind.application.module.user.trader.command.TraderCalendlyInput
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderAlreadyExistsException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderCalendlyUserUriAlreadyTakenException
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class CreateNewTraderCommandHandlerTest(
    @Autowired private val underTest: CreateNewTraderCommandHandler,
    @Autowired private val traderRepository: TraderRepository,
    @Autowired private val appUserRepository: AppUserRepository,
    @Autowired private val appFileFinderService: AppFileFinderService,
) : IntegrationTest() {

    @Test
    fun `should create new trader and change role of a user`() {
        dataHelper.getAppUser(id = 1.toUUID(), hubspotIdentifier = 1, userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), phone = "+************", country = Country.CZ)

        every { hubspotService.updateCrmUserRole(any(), any()) } just Runs
        every { hubspotService.createCustomerTextProperty(any(), any()) } just Runs

        val result = underTest.handle(
            defaultCommand(
                userId = 1.toUUID(),
                firstName = "John",
                lastName = "Doe",
                biography = "I am a trader",
                tags = listOf(TraderTag.MINDSET),
                badgeColor = BadgeColor.GREEN_GRADIENT,
                commentControl = true,
                socialLinkInstagram = "instagram",
                socialLinkLinkedin = "linkedin",
                socialLinkFacebook = "facebook",
                socialLinkTwitter = "twitter",
                calendly = TraderCalendlyInput("calendly", "calendly-user-uri"),
                checkoutVideoUrl = "checkout-video-url.com",
            ),
        )

        result.id shouldBe 1.toUUID()

        traderRepository.findByIdOrNull(result.id)!!.run {
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            phone shouldBe "+************"
            biography shouldBe "I am a trader"
            tags shouldBe listOf(TraderTag.MINDSET)
            country shouldBe Country.CZ
            badgeColor shouldBe BadgeColor.GREEN_GRADIENT
            commentControl shouldBe true
            socialLinkInstagram shouldBe "instagram"
            socialLinkLinkedin shouldBe "linkedin"
            socialLinkFacebook shouldBe "facebook"
            socialLinkTwitter shouldBe "twitter"
            calendlyUrl shouldBe "calendly"
            calendlyUserUri shouldBe "calendly-user-uri"
        }

        appUserRepository.findByIdOrNull(1.toUUID())!!.role shouldBe UserRole.TRADER

        verifySequence {
            hubspotService.updateCrmUserRole(1, UserRole.TRADER)
            hubspotService.createCustomerTextProperty("mentor_00000001", "Mentor John Doe")
        }
    }

    @Test
    fun `should create new trader and copy profile picture from student`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = 1.toUUID(),
            profilePictureFileId = dataHelper.getImage(
                type = FileType.STUDENT_PROFILE_PICTURE,
                originalFileUrl = "url",
                compressedFileUrl = "url-comp",
                blurHash = "123",
                extension = "png",
            ).id,
        )

        every { hubspotService.updateCrmUserRole(any(), any()) } just Runs
        every { hubspotService.createCustomerTextProperty(any(), any()) } just Runs

        val result = underTest.handle(
            defaultCommand(
                userId = 1.toUUID(),
            ),
        )

        result.id shouldBe 1.toUUID()

        traderRepository.findByIdOrNull(result.id)!!.run {
            profilePictureFileId shouldNotBe null
            appFileFinderService.getById(profilePictureFileId!!).run {
                originalFileUrl shouldNotBe "url" // url is different from Student ProfilePicture
                compressedFileUrl shouldNotBe "url-comp" // url is different from Student ProfilePicture
                originalFileUrl shouldContain "-orig"
                compressedFileUrl shouldContain "-comp"
                blurHash shouldBe "123" // blurHash remains the same for copied file
                extension shouldBe "png" // extension remains the same for copied file
                type shouldBe FileType.TRADER_PROFILE_PICTURE
            }
        }

        verifySequence {
            hubspotService.updateCrmUserRole(1, UserRole.TRADER)
            hubspotService.createCustomerTextProperty("mentor_00000001", "Mentor John Doe")
        }
    }

    @Test
    fun `should throw if trader already exists`() {
        dataHelper.getTrader(id = 1.toUUID())

        shouldThrow<TraderAlreadyExistsException> {
            underTest.handle(
                defaultCommand(userId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if user has not yet finished onboarding`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)

        shouldThrow<OnboardingNotYetFinishedException> {
            underTest.handle(
                defaultCommand(userId = 1.toUUID()),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = [UserRole.STUDENT_ROLE],
        mode = EnumSource.Mode.EXCLUDE,
    )
    fun `should throw if user has other role than STUDENT`(wrongRole: UserRole) {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = wrongRole)

        shouldThrow<UserHasWrongRoleException> {
            underTest.handle(
                defaultCommand(userId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if calendly user uri is already taken`() {
        dataHelper.getTrader(
            calendlyUrl = "calendly-url",
            calendlyUserUri = "existing-calendly-user-uri",
        )

        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        shouldThrow<TraderCalendlyUserUriAlreadyTakenException> {
            underTest.handle(
                defaultCommand(
                    userId = 1.toUUID(),
                    calendly = TraderCalendlyInput("calendly-url", "existing-calendly-user-uri"),
                ),
            )
        }
    }

    private fun defaultCommand(
        userId: UUID,
        position: String = "Trader",
        firstName: String = "John",
        lastName: String = "Doe",
        biography: String? = "I am a trader",
        tags: List<TraderTag> = listOf(TraderTag.MINDSET),
        badgeColor: BadgeColor = BadgeColor.GREEN_GRADIENT,
        commentControl: Boolean = false,
        socialLinkInstagram: String? = "instagram",
        socialLinkLinkedin: String? = "linkedin",
        socialLinkFacebook: String? = "facebook",
        socialLinkTwitter: String? = "twitter",
        calendly: TraderCalendlyInput? = TraderCalendlyInput("calendly", "calendly-user-uri"),
        checkoutVideoUrl: String? = "checkout-video-url.com",
    ) = CreateNewTraderCommand(
        studentId = userId,
        position = position,
        firstName = firstName,
        lastName = lastName,
        biography = biography,
        tags = tags,
        badgeColor = badgeColor,
        commentControl = commentControl,
        socialLinkInstagram = socialLinkInstagram,
        socialLinkLinkedin = socialLinkLinkedin,
        socialLinkFacebook = socialLinkFacebook,
        socialLinkTwitter = socialLinkTwitter,
        calendly = calendly,
        checkoutVideoUrl = checkoutVideoUrl,
    )
}
