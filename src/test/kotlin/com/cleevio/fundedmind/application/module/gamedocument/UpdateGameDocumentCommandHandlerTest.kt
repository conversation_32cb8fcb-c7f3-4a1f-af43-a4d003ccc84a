package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.UpdateGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class UpdateGameDocumentCommandHandlerTest(
    @Autowired private val underTest: UpdateGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should update game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 105.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to update
        val gameDocument = dataHelper.getGameDocument(
            id = 201.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            reachedLevel = GameLevel.ONE,
            payoutDate = LocalDate.now().minusDays(1),
            truthScore = 80,
            scoreMessage = "Initial achievement",
        )

        // Update the game document
        underTest.handle(
            defaultCommand(
                gameDocumentId = gameDocument.id,
                type = GameDocumentType.PAYOUT,
                issuingCompany = IssuingCompany.FTMO,
                payoutAmount = BigDecimal("1000.00"),
                reachedLevel = GameLevel.TWO,
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Updated achievement!",
            ),
        )

        // Verify the update
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.PAYOUT
            issuingCompany shouldBe IssuingCompany.FTMO
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("1000.00")
            reachedLevel shouldBe GameLevel.TWO
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 95
            scoreMessage shouldBe "Updated achievement!"
        }
    }

    @Test
    fun `should update game document type from payout to certificate`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 106.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to update
        val gameDocument = dataHelper.getGameDocument(
            id = 202.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            reachedLevel = GameLevel.ONE,
            payoutDate = LocalDate.now().minusDays(1),
            truthScore = 80,
            scoreMessage = "Initial achievement",
        )

        // Update the game document
        underTest.handle(
            defaultCommand(
                gameDocumentId = gameDocument.id,
                type = GameDocumentType.CERTIFICATE,
                issuingCompany = IssuingCompany.FTMO,
                payoutAmount = null,
                reachedLevel = GameLevel.THREE,
                truthScore = 100,
                scoreMessage = "Certificate awarded!",
            ),
        )

        // Verify the update
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            type shouldBe GameDocumentType.CERTIFICATE
            issuingCompany shouldBe IssuingCompany.FTMO
            payoutAmount shouldBe null
            reachedLevel shouldBe GameLevel.THREE
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 100
            scoreMessage shouldBe "Certificate awarded!"
        }
    }

    @Test
    fun `should throw exception when payout amount is null for payout document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to update
        val gameDocument = dataHelper.getGameDocument(
            id = 204.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 100.toBigDecimal(),
        )

        // Attempt to update with invalid data
        shouldThrow<GameDocumentPayoutAmountIsRequiredException> {
            underTest.handle(
                defaultCommand(
                    gameDocumentId = gameDocument.id,
                    payoutAmount = null,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to update
        val gameDocument = dataHelper.getGameDocument(
            id = 204.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            reachedLevel = GameLevel.ONE,
            payoutDate = LocalDate.now().minusDays(1),
            truthScore = 80,
            scoreMessage = "Initial achievement",
        )

        // Attempt to update with invalid data
        shouldThrow<GameDocumentPayoutAmountCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    gameDocumentId = gameDocument.id,
                    payoutAmount = BigDecimal("-100.00"),
                ),
            )
        }
    }

    @Test
    fun `should throw exception when truth score is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 109.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to update
        val gameDocument = dataHelper.getGameDocument(
            id = 205.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            reachedLevel = GameLevel.ONE,
            payoutDate = LocalDate.now().minusDays(1),
            truthScore = 80,
            scoreMessage = "Initial achievement",
        )

        // Attempt to update with invalid data
        shouldThrow<GameDocumentTruthScoreCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    gameDocumentId = gameDocument.id,
                    truthScore = -10,
                ),
            )
        }
    }

    private fun defaultCommand(
        gameDocumentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FTMO,
        payoutAmount: BigDecimal? = BigDecimal("1000.00"),
        reachedLevel: GameLevel = GameLevel.TWO,
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 95,
        scoreMessage: String? = "Updated achievement!",
    ) = UpdateGameDocumentCommand(
        gameDocumentId = gameDocumentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        reachedLevel = reachedLevel,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
