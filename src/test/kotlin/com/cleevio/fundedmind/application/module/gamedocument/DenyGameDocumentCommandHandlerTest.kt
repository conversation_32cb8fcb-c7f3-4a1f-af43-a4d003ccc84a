package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.DenyGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentHasWrongStateException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate

class DenyGameDocumentCommandHandlerTest(
    @Autowired private val underTest: DenyGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should deny game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document to deny
        val gameDocument = dataHelper.getGameDocument(
            id = 401.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            reachedLevel = GameLevel.ONE,
            payoutDate = LocalDate.now(),
            truthScore = 80,
            scoreMessage = "Achievement to deny",
        )

        // Verify initial state
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }

        every { sendEmailService.sendEmailGameDocumentDenied(401.toUUID()) } just Runs

        // Deny the game document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                denyMessage = "Document does not meet our standards",
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify the denial
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Document does not meet our standards"
            deniedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
        }

        verify { sendEmailService.sendEmailGameDocumentDenied(401.toUUID()) }
    }

    @Test
    fun `should throw exception when game document is not in WAITING state`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document that is already approved
        val gameDocument = dataHelper.getGameDocument(
            id = 304.toUUID(),
            studentId = user.id,
            entityModifier = { it.deny("already denied") },
        )

        // Try to approve the already approved document
        shouldThrow<GameDocumentHasWrongStateException> {
            underTest.handle(
                DenyGameDocumentCommand(
                    gameDocumentId = gameDocument.id,
                    denyMessage = "Lol git gud",
                    now = "2025-09-15T10:00:00Z".toInstant(),
                ),
            )
        }
    }
}
