package com.cleevio.fundedmind.application.common

import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import io.kotest.assertions.withClue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import jakarta.persistence.Entity
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.reflect.KProperty1
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.findAnnotation

class FileAnnotationTest : ApplicationStructureTest() {

    @Test
    fun `all entity fields ending with 'FileId' of type UUID must have @File annotation`() {
        val entityClasses = scanResult
            .getClassesWithAnnotation(Entity::class.java.name)
            .mapNotNull { it.loadClass() }

        for (entityClass in entityClasses) {
            val kClass = entityClass.kotlin

            for (prop in kClass.declaredMemberProperties) {
                // Check if the field name matches `FileId` and is of type `UUID` or `UUID?`
                if (prop.name.endsWith("FileId") && hasUUIDType(prop)) {
                    val fileAnnotation = prop.findAnnotation<File>()
                    withClue("Field '${prop.name}' in entity '${kClass.simpleName}' must have @File annotation") {
                        fileAnnotation shouldNotBe null
                    }
                }
            }
        }
    }

    @Test
    fun `each FileType must have exactly one @File annotation declaration across all entities`() {
        // Map to track usage count of each FileType
        val fileTypeUsageCount = mutableMapOf<FileType, Int>()

        // Reflect on all classes within the scanned package
        val entityClasses = scanResult
            .getClassesWithAnnotation(Entity::class.java.name)
            .mapNotNull { it.loadClass() }

        // Collect all @File annotations from properties in the scanned classes
        for (entityClass in entityClasses) {
            val kClass = entityClass.kotlin

            kClass.declaredMemberProperties.forEach { property ->
                property.findAnnotation<File>()?.let { fileAnnotation ->
                    val fileType = fileAnnotation.type
                    fileTypeUsageCount[fileType] = fileTypeUsageCount.getOrDefault(fileType, 0) + 1
                }
            }
        }

        // Verify each FileType is used exactly once
        FileType.entries.forEach { fileType ->
            withClue(
                "'FileType.$fileType' was expected to be declared exactly once across entities via @File annotation",
            ) {
                val usageCount = fileTypeUsageCount[fileType] ?: 0
                usageCount shouldBe 1
            }
        }
    }

    private fun hasUUIDType(prop: KProperty1<out Any, *>): Boolean = prop.returnType.classifier == UUID::class
}
